<?php
/**
 * Template Name: Medical Device Page
 * Description: Custom Medical Device page template for Krystelis_Custom Theme
 *
 * @package Krystelis_Custom
 */

get_header();
?>
<section class="medical-hero-wrapper">
  <div class="medical-hero-content">
    <div class="medical-hero-text">
      <div class="medical-hero-subtitle">Making clinical research crystal clear</div>
      <div class="medical-hero-title">Krystelis Medical Device Services</div>
      <div class="medical-hero-description">
        Krystelis offers comprehensive medical device consulting services to support pharmaceutical and medical device companies navigate complex regulatory environments. Our expert team provides end-to-end assistance, ensuring compliance with global regulations, from product conception through post-market surveillance.
      </div>
    </div>
    <div class="medical-hero-image">
      <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Medical-Device.jpg" alt="Medical Device Services" />
    </div>
  </div>
</section>


<section class="medical-services-cards-wrapper">
  <div class="medical-services-cards">
    <!-- Card 1 -->
    <div class="medical-service-card">
      <div class="medical-service-card-header">
        <span class="medical-service-icon">
<svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90" fill="none"><path d="M88 8.84106V63.7389C86.9711 67.5803 84.2465 68.5821 81.0183 68.5439C72.3881 68.4441 63.7579 68.5099 55.1278 68.5099H54.0281C54.1588 69.4289 54.2253 70.2438 54.3946 71.0354C55.1942 74.7813 57.2414 76.9864 61.3485 76.91C62.4246 76.8909 63.5136 76.8824 64.5768 77.0246C67.4492 77.413 69.0291 80.5668 67.6529 83.0881C66.7525 84.7371 65.2842 85.3462 63.4535 85.3441C51.475 85.3335 39.4987 85.3398 27.5202 85.3377C27.2716 85.3377 27.0229 85.3207 26.7742 85.2974C24.5513 85.1064 22.9372 83.4404 22.8579 81.2565C22.7785 79.1151 24.3327 77.256 26.5341 77.0119C27.7924 76.8718 29.0743 76.9206 30.3433 76.8824C31.9489 76.8357 33.4023 76.3858 34.547 75.2228C36.3647 73.3764 36.7334 71.027 36.9264 68.5057H35.846C26.9393 68.5057 18.0326 68.5099 9.12376 68.5057C5.37673 68.5035 3.00161 66.1563 3.00161 62.4528C2.99946 44.9819 2.99946 27.5089 3.00161 10.0359C3.00375 6.37494 5.34672 4.01068 9.06159 4.00856C33.3487 3.99582 57.6358 3.9937 81.9229 4.02766C82.9111 4.02766 83.9636 4.27172 84.8768 4.65799C86.6903 5.42839 87.5456 7.01375 88 8.84106ZM79.4577 54.3668V12.4808H11.531V54.3668H79.4577ZM5.82903 57.3571C5.82903 59.0253 5.82903 60.6106 5.82903 62.196C5.82903 64.7216 6.83652 65.7127 9.40242 65.7127C33.4666 65.7127 57.5286 65.7127 81.5928 65.7127C81.8693 65.7127 82.1458 65.7191 82.4223 65.7042C83.7749 65.6278 84.9882 64.7407 85.0761 63.4291C85.2133 61.4235 85.1126 59.4031 85.1126 57.3571H5.82903ZM8.65645 54.405V53.3842C8.65645 39.5233 8.65645 25.6625 8.65645 11.7996C8.65645 10.002 9.03587 9.61995 10.8279 9.61995C33.9317 9.61995 57.0356 9.61995 80.1394 9.61995C81.9786 9.61995 82.343 9.98924 82.343 11.8505C82.343 25.6837 82.343 39.5191 82.343 53.3523C82.343 53.6961 82.343 54.04 82.343 54.3838H85.1704V53.4945C85.1704 39.0586 85.1704 24.6205 85.1704 10.1845C85.1704 7.86056 84.1265 6.81426 81.79 6.81426C57.6079 6.81426 33.4259 6.81426 9.24165 6.81426C6.85582 6.81426 5.82688 7.84994 5.82688 10.2354C5.82688 24.6438 5.82688 39.0543 5.82688 53.4627V54.405H8.6543H8.65645ZM51.1921 68.5523H39.8009C39.5673 71.5406 38.9306 74.3887 36.9349 76.8188H54.0774C52.0452 74.3632 51.4386 71.5257 51.1921 68.5523ZM45.5308 82.532C51.3957 82.532 57.2606 82.532 63.1277 82.5299C63.4857 82.5299 63.8522 82.5384 64.2017 82.4705C64.8833 82.3389 65.2563 81.8911 65.2885 81.2077C65.3206 80.5286 64.9991 80.0404 64.3346 79.843C64.0237 79.7497 63.6808 79.7284 63.3528 79.7306C62.1631 79.7348 60.9755 79.7836 59.7858 79.7815C49.1064 79.7666 38.429 79.7433 27.7496 79.7263C26.3862 79.7242 25.7196 80.1784 25.7003 81.1058C25.6789 82.0736 26.3369 82.532 27.7667 82.532C33.6874 82.532 39.608 82.532 45.5287 82.532H45.5308ZM67.1748 41.9003C68.3495 43.0443 69.3828 44.0333 70.3967 45.0392C71.342 45.9773 71.3463 46.6373 70.4181 47.569C69.252 48.7384 68.0816 49.9036 66.8983 51.0539C66.0473 51.8816 65.3163 51.8795 64.461 51.039C63.4214 50.0182 62.4032 48.974 61.1449 47.707C60.6304 48.3203 60.1952 48.9167 59.6829 49.4388C58.3603 50.7843 57.2928 50.5191 56.7505 48.7448C56.4139 47.6454 56.0516 46.5546 55.7644 45.4425C55.6079 44.8355 55.325 44.6509 54.6819 44.653C44.3926 44.6763 34.1054 44.67 23.8161 44.67C23.5953 44.67 23.3745 44.6742 23.1515 44.67C21.9533 44.6466 21.4302 44.1436 21.4281 42.9572C21.4195 39.1243 21.4259 35.2893 21.4238 31.4564C21.4238 26.6642 21.4174 21.8721 21.4195 17.0799C21.4195 15.6558 21.8525 15.2101 23.2844 15.2101C36.1997 15.2059 49.1171 15.2059 62.0323 15.2101C63.4964 15.2101 63.9273 15.6643 63.9273 17.1436C63.9273 23.277 63.9316 29.4105 63.9101 35.544C63.9101 36.1255 64.0452 36.4184 64.6561 36.5712C65.8865 36.8747 67.0998 37.2588 68.311 37.6324C69.7214 38.0696 70.0087 39.2114 68.9648 40.2555C68.4224 40.7989 67.8351 41.2955 67.1748 41.8982V41.9003ZM24.3155 25.0704V41.8409H54.609C53.9874 39.7632 53.385 37.7257 52.7655 35.6947C52.5661 35.0389 52.4482 34.4086 53.0034 33.861C53.5543 33.3156 54.1889 33.4174 54.8555 33.6169C56.9069 34.2303 58.9648 34.8267 61.027 35.4315V25.0683H24.3155V25.0704ZM24.2769 22.1692H61.0355V18.1028H24.2769V22.1692ZM56.1803 37.0063C57.132 40.1855 58.0195 43.1504 58.9305 46.1895C59.1663 45.9773 59.3121 45.8542 59.4471 45.7205C60.8619 44.3452 61.6958 44.3368 63.0741 45.7141C63.9273 46.5673 64.7633 47.4375 65.5478 48.2397C66.2724 47.5499 66.9219 46.9302 67.6529 46.2341C66.6389 45.2451 65.6057 44.2646 64.6068 43.2544C63.7065 42.3418 63.7108 41.5204 64.6175 40.6185C64.879 40.3574 65.2413 40.194 65.5564 39.986L65.3463 39.6868C62.356 38.8124 59.3657 37.938 56.176 37.0063H56.1803ZM41.5094 60.1119C40.4976 60.1225 39.8867 60.634 39.8588 61.4575C39.831 62.3043 40.474 62.8985 41.503 62.9049C44.1803 62.9197 46.8599 62.9218 49.5372 62.9049C50.519 62.8985 51.1471 62.3191 51.1492 61.5063C51.1492 60.6828 50.5297 60.1268 49.5458 60.1162C48.1932 60.0992 46.8406 60.1119 45.4879 60.1119C44.1632 60.1119 42.8363 60.1013 41.5115 60.114L41.5094 60.1119ZM34.1268 39.0692C32.3862 39.0692 30.6456 39.0777 28.905 39.067C27.6102 39.0586 27.0915 38.5471 27.0829 37.2716C27.0722 35.6586 27.0722 34.0435 27.0829 32.4306C27.0915 31.1763 27.5802 30.6648 28.8235 30.6606C32.3862 30.6478 35.951 30.6478 39.5137 30.6606C40.7356 30.6648 41.2222 31.1445 41.2372 32.352C41.2565 33.9926 41.2522 35.6353 41.2372 37.2758C41.2264 38.5662 40.7248 39.0607 39.428 39.0692C37.6595 39.0798 35.891 39.0713 34.1247 39.0713L34.1268 39.0692ZM38.354 36.2253V33.5151H29.9768V36.2253H38.354Z" fill="white"/></svg>        </span>
        <span class="medical-service-card-title">Regulatory Compliance</span>
      </div>
      <div class="medical-service-card-body">
        <ul class="medical-service-list">
          <li>Registration of Establishment</li>
          <li>Premarket Notification 510(k) and Premarket Approval (PMA)</li>
          <li>Investigational Device Exemption (IDE)</li>
          <li>Medical Device Reporting (MDR)</li>
          <li>Quality System (QS) regulation</li>
          <li>ISO 13485 certification support</li>
        </ul>
      </div>
    </div>
    <!-- Card 2 -->
    <div class="medical-service-card">
      <div class="medical-service-card-header">
        <span class="medical-service-icon">
<svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90" fill="none"><path d="M88 8.84106V63.7389C86.9711 67.5803 84.2465 68.5821 81.0183 68.5439C72.3881 68.4441 63.7579 68.5099 55.1278 68.5099H54.0281C54.1588 69.4289 54.2253 70.2438 54.3946 71.0354C55.1942 74.7813 57.2414 76.9864 61.3485 76.91C62.4246 76.8909 63.5136 76.8824 64.5768 77.0246C67.4492 77.413 69.0291 80.5668 67.6529 83.0881C66.7525 84.7371 65.2842 85.3462 63.4535 85.3441C51.475 85.3335 39.4987 85.3398 27.5202 85.3377C27.2716 85.3377 27.0229 85.3207 26.7742 85.2974C24.5513 85.1064 22.9372 83.4404 22.8579 81.2565C22.7785 79.1151 24.3327 77.256 26.5341 77.0119C27.7924 76.8718 29.0743 76.9206 30.3433 76.8824C31.9489 76.8357 33.4023 76.3858 34.547 75.2228C36.3647 73.3764 36.7334 71.027 36.9264 68.5057H35.846C26.9393 68.5057 18.0326 68.5099 9.12376 68.5057C5.37673 68.5035 3.00161 66.1563 3.00161 62.4528C2.99946 44.9819 2.99946 27.5089 3.00161 10.0359C3.00375 6.37494 5.34672 4.01068 9.06159 4.00856C33.3487 3.99582 57.6358 3.9937 81.9229 4.02766C82.9111 4.02766 83.9636 4.27172 84.8768 4.65799C86.6903 5.42839 87.5456 7.01375 88 8.84106ZM79.4577 54.3668V12.4808H11.531V54.3668H79.4577ZM5.82903 57.3571C5.82903 59.0253 5.82903 60.6106 5.82903 62.196C5.82903 64.7216 6.83652 65.7127 9.40242 65.7127C33.4666 65.7127 57.5286 65.7127 81.5928 65.7127C81.8693 65.7127 82.1458 65.7191 82.4223 65.7042C83.7749 65.6278 84.9882 64.7407 85.0761 63.4291C85.2133 61.4235 85.1126 59.4031 85.1126 57.3571H5.82903ZM8.65645 54.405V53.3842C8.65645 39.5233 8.65645 25.6625 8.65645 11.7996C8.65645 10.002 9.03587 9.61995 10.8279 9.61995C33.9317 9.61995 57.0356 9.61995 80.1394 9.61995C81.9786 9.61995 82.343 9.98924 82.343 11.8505C82.343 25.6837 82.343 39.5191 82.343 53.3523C82.343 53.6961 82.343 54.04 82.343 54.3838H85.1704V53.4945C85.1704 39.0586 85.1704 24.6205 85.1704 10.1845C85.1704 7.86056 84.1265 6.81426 81.79 6.81426C57.6079 6.81426 33.4259 6.81426 9.24165 6.81426C6.85582 6.81426 5.82688 7.84994 5.82688 10.2354C5.82688 24.6438 5.82688 39.0543 5.82688 53.4627V54.405H8.6543H8.65645ZM51.1921 68.5523H39.8009C39.5673 71.5406 38.9306 74.3887 36.9349 76.8188H54.0774C52.0452 74.3632 51.4386 71.5257 51.1921 68.5523ZM45.5308 82.532C51.3957 82.532 57.2606 82.532 63.1277 82.5299C63.4857 82.5299 63.8522 82.5384 64.2017 82.4705C64.8833 82.3389 65.2563 81.8911 65.2885 81.2077C65.3206 80.5286 64.9991 80.0404 64.3346 79.843C64.0237 79.7497 63.6808 79.7284 63.3528 79.7306C62.1631 79.7348 60.9755 79.7836 59.7858 79.7815C49.1064 79.7666 38.429 79.7433 27.7496 79.7263C26.3862 79.7242 25.7196 80.1784 25.7003 81.1058C25.6789 82.0736 26.3369 82.532 27.7667 82.532C33.6874 82.532 39.608 82.532 45.5287 82.532H45.5308ZM67.1748 41.9003C68.3495 43.0443 69.3828 44.0333 70.3967 45.0392C71.342 45.9773 71.3463 46.6373 70.4181 47.569C69.252 48.7384 68.0816 49.9036 66.8983 51.0539C66.0473 51.8816 65.3163 51.8795 64.461 51.039C63.4214 50.0182 62.4032 48.974 61.1449 47.707C60.6304 48.3203 60.1952 48.9167 59.6829 49.4388C58.3603 50.7843 57.2928 50.5191 56.7505 48.7448C56.4139 47.6454 56.0516 46.5546 55.7644 45.4425C55.6079 44.8355 55.325 44.6509 54.6819 44.653C44.3926 44.6763 34.1054 44.67 23.8161 44.67C23.5953 44.67 23.3745 44.6742 23.1515 44.67C21.9533 44.6466 21.4302 44.1436 21.4281 42.9572C21.4195 39.1243 21.4259 35.2893 21.4238 31.4564C21.4238 26.6642 21.4174 21.8721 21.4195 17.0799C21.4195 15.6558 21.8525 15.2101 23.2844 15.2101C36.1997 15.2059 49.1171 15.2059 62.0323 15.2101C63.4964 15.2101 63.9273 15.6643 63.9273 17.1436C63.9273 23.277 63.9316 29.4105 63.9101 35.544C63.9101 36.1255 64.0452 36.4184 64.6561 36.5712C65.8865 36.8747 67.0998 37.2588 68.311 37.6324C69.7214 38.0696 70.0087 39.2114 68.9648 40.2555C68.4224 40.7989 67.8351 41.2955 67.1748 41.8982V41.9003ZM24.3155 25.0704V41.8409H54.609C53.9874 39.7632 53.385 37.7257 52.7655 35.6947C52.5661 35.0389 52.4482 34.4086 53.0034 33.861C53.5543 33.3156 54.1889 33.4174 54.8555 33.6169C56.9069 34.2303 58.9648 34.8267 61.027 35.4315V25.0683H24.3155V25.0704ZM24.2769 22.1692H61.0355V18.1028H24.2769V22.1692ZM56.1803 37.0063C57.132 40.1855 58.0195 43.1504 58.9305 46.1895C59.1663 45.9773 59.3121 45.8542 59.4471 45.7205C60.8619 44.3452 61.6958 44.3368 63.0741 45.7141C63.9273 46.5673 64.7633 47.4375 65.5478 48.2397C66.2724 47.5499 66.9219 46.9302 67.6529 46.2341C66.6389 45.2451 65.6057 44.2646 64.6068 43.2544C63.7065 42.3418 63.7108 41.5204 64.6175 40.6185C64.879 40.3574 65.2413 40.194 65.5564 39.986L65.3463 39.6868C62.356 38.8124 59.3657 37.938 56.176 37.0063H56.1803ZM41.5094 60.1119C40.4976 60.1225 39.8867 60.634 39.8588 61.4575C39.831 62.3043 40.474 62.8985 41.503 62.9049C44.1803 62.9197 46.8599 62.9218 49.5372 62.9049C50.519 62.8985 51.1471 62.3191 51.1492 61.5063C51.1492 60.6828 50.5297 60.1268 49.5458 60.1162C48.1932 60.0992 46.8406 60.1119 45.4879 60.1119C44.1632 60.1119 42.8363 60.1013 41.5115 60.114L41.5094 60.1119ZM34.1268 39.0692C32.3862 39.0692 30.6456 39.0777 28.905 39.067C27.6102 39.0586 27.0915 38.5471 27.0829 37.2716C27.0722 35.6586 27.0722 34.0435 27.0829 32.4306C27.0915 31.1763 27.5802 30.6648 28.8235 30.6606C32.3862 30.6478 35.951 30.6478 39.5137 30.6606C40.7356 30.6648 41.2222 31.1445 41.2372 32.352C41.2565 33.9926 41.2522 35.6353 41.2372 37.2758C41.2264 38.5662 40.7248 39.0607 39.428 39.0692C37.6595 39.0798 35.891 39.0713 34.1247 39.0713L34.1268 39.0692ZM38.354 36.2253V33.5151H29.9768V36.2253H38.354Z" fill="white"/></svg>        </span>
        <span class="medical-service-card-title">Regulatory Strategy Development</span>
      </div>
      <div class="medical-service-card-body">
        <ul class="medical-service-list">
          <li>Customised regulatory strategies for FDA approval</li>
          <li>Preparation of technical files, device master files, design dossiers, and design history files</li>
        </ul>
      </div>
    </div>
    <!-- Card 3 -->
    <div class="medical-service-card">
      <div class="medical-service-card-header">
        <span class="medical-service-icon">
<svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90" fill="none"><path d="M88 8.84106V63.7389C86.9711 67.5803 84.2465 68.5821 81.0183 68.5439C72.3881 68.4441 63.7579 68.5099 55.1278 68.5099H54.0281C54.1588 69.4289 54.2253 70.2438 54.3946 71.0354C55.1942 74.7813 57.2414 76.9864 61.3485 76.91C62.4246 76.8909 63.5136 76.8824 64.5768 77.0246C67.4492 77.413 69.0291 80.5668 67.6529 83.0881C66.7525 84.7371 65.2842 85.3462 63.4535 85.3441C51.475 85.3335 39.4987 85.3398 27.5202 85.3377C27.2716 85.3377 27.0229 85.3207 26.7742 85.2974C24.5513 85.1064 22.9372 83.4404 22.8579 81.2565C22.7785 79.1151 24.3327 77.256 26.5341 77.0119C27.7924 76.8718 29.0743 76.9206 30.3433 76.8824C31.9489 76.8357 33.4023 76.3858 34.547 75.2228C36.3647 73.3764 36.7334 71.027 36.9264 68.5057H35.846C26.9393 68.5057 18.0326 68.5099 9.12376 68.5057C5.37673 68.5035 3.00161 66.1563 3.00161 62.4528C2.99946 44.9819 2.99946 27.5089 3.00161 10.0359C3.00375 6.37494 5.34672 4.01068 9.06159 4.00856C33.3487 3.99582 57.6358 3.9937 81.9229 4.02766C82.9111 4.02766 83.9636 4.27172 84.8768 4.65799C86.6903 5.42839 87.5456 7.01375 88 8.84106ZM79.4577 54.3668V12.4808H11.531V54.3668H79.4577ZM5.82903 57.3571C5.82903 59.0253 5.82903 60.6106 5.82903 62.196C5.82903 64.7216 6.83652 65.7127 9.40242 65.7127C33.4666 65.7127 57.5286 65.7127 81.5928 65.7127C81.8693 65.7127 82.1458 65.7191 82.4223 65.7042C83.7749 65.6278 84.9882 64.7407 85.0761 63.4291C85.2133 61.4235 85.1126 59.4031 85.1126 57.3571H5.82903ZM8.65645 54.405V53.3842C8.65645 39.5233 8.65645 25.6625 8.65645 11.7996C8.65645 10.002 9.03587 9.61995 10.8279 9.61995C33.9317 9.61995 57.0356 9.61995 80.1394 9.61995C81.9786 9.61995 82.343 9.98924 82.343 11.8505C82.343 25.6837 82.343 39.5191 82.343 53.3523C82.343 53.6961 82.343 54.04 82.343 54.3838H85.1704V53.4945C85.1704 39.0586 85.1704 24.6205 85.1704 10.1845C85.1704 7.86056 84.1265 6.81426 81.79 6.81426C57.6079 6.81426 33.4259 6.81426 9.24165 6.81426C6.85582 6.81426 5.82688 7.84994 5.82688 10.2354C5.82688 24.6438 5.82688 39.0543 5.82688 53.4627V54.405H8.6543H8.65645ZM51.1921 68.5523H39.8009C39.5673 71.5406 38.9306 74.3887 36.9349 76.8188H54.0774C52.0452 74.3632 51.4386 71.5257 51.1921 68.5523ZM45.5308 82.532C51.3957 82.532 57.2606 82.532 63.1277 82.5299C63.4857 82.5299 63.8522 82.5384 64.2017 82.4705C64.8833 82.3389 65.2563 81.8911 65.2885 81.2077C65.3206 80.5286 64.9991 80.0404 64.3346 79.843C64.0237 79.7497 63.6808 79.7284 63.3528 79.7306C62.1631 79.7348 60.9755 79.7836 59.7858 79.7815C49.1064 79.7666 38.429 79.7433 27.7496 79.7263C26.3862 79.7242 25.7196 80.1784 25.7003 81.1058C25.6789 82.0736 26.3369 82.532 27.7667 82.532C33.6874 82.532 39.608 82.532 45.5287 82.532H45.5308ZM67.1748 41.9003C68.3495 43.0443 69.3828 44.0333 70.3967 45.0392C71.342 45.9773 71.3463 46.6373 70.4181 47.569C69.252 48.7384 68.0816 49.9036 66.8983 51.0539C66.0473 51.8816 65.3163 51.8795 64.461 51.039C63.4214 50.0182 62.4032 48.974 61.1449 47.707C60.6304 48.3203 60.1952 48.9167 59.6829 49.4388C58.3603 50.7843 57.2928 50.5191 56.7505 48.7448C56.4139 47.6454 56.0516 46.5546 55.7644 45.4425C55.6079 44.8355 55.325 44.6509 54.6819 44.653C44.3926 44.6763 34.1054 44.67 23.8161 44.67C23.5953 44.67 23.3745 44.6742 23.1515 44.67C21.9533 44.6466 21.4302 44.1436 21.4281 42.9572C21.4195 39.1243 21.4259 35.2893 21.4238 31.4564C21.4238 26.6642 21.4174 21.8721 21.4195 17.0799C21.4195 15.6558 21.8525 15.2101 23.2844 15.2101C36.1997 15.2059 49.1171 15.2059 62.0323 15.2101C63.4964 15.2101 63.9273 15.6643 63.9273 17.1436C63.9273 23.277 63.9316 29.4105 63.9101 35.544C63.9101 36.1255 64.0452 36.4184 64.6561 36.5712C65.8865 36.8747 67.0998 37.2588 68.311 37.6324C69.7214 38.0696 70.0087 39.2114 68.9648 40.2555C68.4224 40.7989 67.8351 41.2955 67.1748 41.8982V41.9003ZM24.3155 25.0704V41.8409H54.609C53.9874 39.7632 53.385 37.7257 52.7655 35.6947C52.5661 35.0389 52.4482 34.4086 53.0034 33.861C53.5543 33.3156 54.1889 33.4174 54.8555 33.6169C56.9069 34.2303 58.9648 34.8267 61.027 35.4315V25.0683H24.3155V25.0704ZM24.2769 22.1692H61.0355V18.1028H24.2769V22.1692ZM56.1803 37.0063C57.132 40.1855 58.0195 43.1504 58.9305 46.1895C59.1663 45.9773 59.3121 45.8542 59.4471 45.7205C60.8619 44.3452 61.6958 44.3368 63.0741 45.7141C63.9273 46.5673 64.7633 47.4375 65.5478 48.2397C66.2724 47.5499 66.9219 46.9302 67.6529 46.2341C66.6389 45.2451 65.6057 44.2646 64.6068 43.2544C63.7065 42.3418 63.7108 41.5204 64.6175 40.6185C64.879 40.3574 65.2413 40.194 65.5564 39.986L65.3463 39.6868C62.356 38.8124 59.3657 37.938 56.176 37.0063H56.1803ZM41.5094 60.1119C40.4976 60.1225 39.8867 60.634 39.8588 61.4575C39.831 62.3043 40.474 62.8985 41.503 62.9049C44.1803 62.9197 46.8599 62.9218 49.5372 62.9049C50.519 62.8985 51.1471 62.3191 51.1492 61.5063C51.1492 60.6828 50.5297 60.1268 49.5458 60.1162C48.1932 60.0992 46.8406 60.1119 45.4879 60.1119C44.1632 60.1119 42.8363 60.1013 41.5115 60.114L41.5094 60.1119ZM34.1268 39.0692C32.3862 39.0692 30.6456 39.0777 28.905 39.067C27.6102 39.0586 27.0915 38.5471 27.0829 37.2716C27.0722 35.6586 27.0722 34.0435 27.0829 32.4306C27.0915 31.1763 27.5802 30.6648 28.8235 30.6606C32.3862 30.6478 35.951 30.6478 39.5137 30.6606C40.7356 30.6648 41.2222 31.1445 41.2372 32.352C41.2565 33.9926 41.2522 35.6353 41.2372 37.2758C41.2264 38.5662 40.7248 39.0607 39.428 39.0692C37.6595 39.0798 35.891 39.0713 34.1247 39.0713L34.1268 39.0692ZM38.354 36.2253V33.5151H29.9768V36.2253H38.354Z" fill="white"/></svg>        </span>
        <span class="medical-service-card-title">Medical Writing for Devices</span>
      </div>
      <div class="medical-service-card-body">
        <ul class="medical-service-list">
          <li>Clinical evaluation plan</li>
          <li>Informed consent</li>
          <li>Investigator’s brochure</li>
          <li>Clinical evaluation reports</li>
          <li>Literature search for real-world evidence collection</li>
          <li>Evidence summaries</li>
          <li>Instruction for use of the device</li>
          <li>Periodic safety update report</li>
          <li>Technical document</li>
          <li>Summary of safety and clinical performance</li>
        </ul>
      </div>
    </div>

<!-- Card 4 -->
<div class="medical-service-card">
      <div class="medical-service-card-header">
        <span class="medical-service-icon">
<svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90" fill="none"><path d="M88 8.84106V63.7389C86.9711 67.5803 84.2465 68.5821 81.0183 68.5439C72.3881 68.4441 63.7579 68.5099 55.1278 68.5099H54.0281C54.1588 69.4289 54.2253 70.2438 54.3946 71.0354C55.1942 74.7813 57.2414 76.9864 61.3485 76.91C62.4246 76.8909 63.5136 76.8824 64.5768 77.0246C67.4492 77.413 69.0291 80.5668 67.6529 83.0881C66.7525 84.7371 65.2842 85.3462 63.4535 85.3441C51.475 85.3335 39.4987 85.3398 27.5202 85.3377C27.2716 85.3377 27.0229 85.3207 26.7742 85.2974C24.5513 85.1064 22.9372 83.4404 22.8579 81.2565C22.7785 79.1151 24.3327 77.256 26.5341 77.0119C27.7924 76.8718 29.0743 76.9206 30.3433 76.8824C31.9489 76.8357 33.4023 76.3858 34.547 75.2228C36.3647 73.3764 36.7334 71.027 36.9264 68.5057H35.846C26.9393 68.5057 18.0326 68.5099 9.12376 68.5057C5.37673 68.5035 3.00161 66.1563 3.00161 62.4528C2.99946 44.9819 2.99946 27.5089 3.00161 10.0359C3.00375 6.37494 5.34672 4.01068 9.06159 4.00856C33.3487 3.99582 57.6358 3.9937 81.9229 4.02766C82.9111 4.02766 83.9636 4.27172 84.8768 4.65799C86.6903 5.42839 87.5456 7.01375 88 8.84106ZM79.4577 54.3668V12.4808H11.531V54.3668H79.4577ZM5.82903 57.3571C5.82903 59.0253 5.82903 60.6106 5.82903 62.196C5.82903 64.7216 6.83652 65.7127 9.40242 65.7127C33.4666 65.7127 57.5286 65.7127 81.5928 65.7127C81.8693 65.7127 82.1458 65.7191 82.4223 65.7042C83.7749 65.6278 84.9882 64.7407 85.0761 63.4291C85.2133 61.4235 85.1126 59.4031 85.1126 57.3571H5.82903ZM8.65645 54.405V53.3842C8.65645 39.5233 8.65645 25.6625 8.65645 11.7996C8.65645 10.002 9.03587 9.61995 10.8279 9.61995C33.9317 9.61995 57.0356 9.61995 80.1394 9.61995C81.9786 9.61995 82.343 9.98924 82.343 11.8505C82.343 25.6837 82.343 39.5191 82.343 53.3523C82.343 53.6961 82.343 54.04 82.343 54.3838H85.1704V53.4945C85.1704 39.0586 85.1704 24.6205 85.1704 10.1845C85.1704 7.86056 84.1265 6.81426 81.79 6.81426C57.6079 6.81426 33.4259 6.81426 9.24165 6.81426C6.85582 6.81426 5.82688 7.84994 5.82688 10.2354C5.82688 24.6438 5.82688 39.0543 5.82688 53.4627V54.405H8.6543H8.65645ZM51.1921 68.5523H39.8009C39.5673 71.5406 38.9306 74.3887 36.9349 76.8188H54.0774C52.0452 74.3632 51.4386 71.5257 51.1921 68.5523ZM45.5308 82.532C51.3957 82.532 57.2606 82.532 63.1277 82.5299C63.4857 82.5299 63.8522 82.5384 64.2017 82.4705C64.8833 82.3389 65.2563 81.8911 65.2885 81.2077C65.3206 80.5286 64.9991 80.0404 64.3346 79.843C64.0237 79.7497 63.6808 79.7284 63.3528 79.7306C62.1631 79.7348 60.9755 79.7836 59.7858 79.7815C49.1064 79.7666 38.429 79.7433 27.7496 79.7263C26.3862 79.7242 25.7196 80.1784 25.7003 81.1058C25.6789 82.0736 26.3369 82.532 27.7667 82.532C33.6874 82.532 39.608 82.532 45.5287 82.532H45.5308ZM67.1748 41.9003C68.3495 43.0443 69.3828 44.0333 70.3967 45.0392C71.342 45.9773 71.3463 46.6373 70.4181 47.569C69.252 48.7384 68.0816 49.9036 66.8983 51.0539C66.0473 51.8816 65.3163 51.8795 64.461 51.039C63.4214 50.0182 62.4032 48.974 61.1449 47.707C60.6304 48.3203 60.1952 48.9167 59.6829 49.4388C58.3603 50.7843 57.2928 50.5191 56.7505 48.7448C56.4139 47.6454 56.0516 46.5546 55.7644 45.4425C55.6079 44.8355 55.325 44.6509 54.6819 44.653C44.3926 44.6763 34.1054 44.67 23.8161 44.67C23.5953 44.67 23.3745 44.6742 23.1515 44.67C21.9533 44.6466 21.4302 44.1436 21.4281 42.9572C21.4195 39.1243 21.4259 35.2893 21.4238 31.4564C21.4238 26.6642 21.4174 21.8721 21.4195 17.0799C21.4195 15.6558 21.8525 15.2101 23.2844 15.2101C36.1997 15.2059 49.1171 15.2059 62.0323 15.2101C63.4964 15.2101 63.9273 15.6643 63.9273 17.1436C63.9273 23.277 63.9316 29.4105 63.9101 35.544C63.9101 36.1255 64.0452 36.4184 64.6561 36.5712C65.8865 36.8747 67.0998 37.2588 68.311 37.6324C69.7214 38.0696 70.0087 39.2114 68.9648 40.2555C68.4224 40.7989 67.8351 41.2955 67.1748 41.8982V41.9003ZM24.3155 25.0704V41.8409H54.609C53.9874 39.7632 53.385 37.7257 52.7655 35.6947C52.5661 35.0389 52.4482 34.4086 53.0034 33.861C53.5543 33.3156 54.1889 33.4174 54.8555 33.6169C56.9069 34.2303 58.9648 34.8267 61.027 35.4315V25.0683H24.3155V25.0704ZM24.2769 22.1692H61.0355V18.1028H24.2769V22.1692ZM56.1803 37.0063C57.132 40.1855 58.0195 43.1504 58.9305 46.1895C59.1663 45.9773 59.3121 45.8542 59.4471 45.7205C60.8619 44.3452 61.6958 44.3368 63.0741 45.7141C63.9273 46.5673 64.7633 47.4375 65.5478 48.2397C66.2724 47.5499 66.9219 46.9302 67.6529 46.2341C66.6389 45.2451 65.6057 44.2646 64.6068 43.2544C63.7065 42.3418 63.7108 41.5204 64.6175 40.6185C64.879 40.3574 65.2413 40.194 65.5564 39.986L65.3463 39.6868C62.356 38.8124 59.3657 37.938 56.176 37.0063H56.1803ZM41.5094 60.1119C40.4976 60.1225 39.8867 60.634 39.8588 61.4575C39.831 62.3043 40.474 62.8985 41.503 62.9049C44.1803 62.9197 46.8599 62.9218 49.5372 62.9049C50.519 62.8985 51.1471 62.3191 51.1492 61.5063C51.1492 60.6828 50.5297 60.1268 49.5458 60.1162C48.1932 60.0992 46.8406 60.1119 45.4879 60.1119C44.1632 60.1119 42.8363 60.1013 41.5115 60.114L41.5094 60.1119ZM34.1268 39.0692C32.3862 39.0692 30.6456 39.0777 28.905 39.067C27.6102 39.0586 27.0915 38.5471 27.0829 37.2716C27.0722 35.6586 27.0722 34.0435 27.0829 32.4306C27.0915 31.1763 27.5802 30.6648 28.8235 30.6606C32.3862 30.6478 35.951 30.6478 39.5137 30.6606C40.7356 30.6648 41.2222 31.1445 41.2372 32.352C41.2565 33.9926 41.2522 35.6353 41.2372 37.2758C41.2264 38.5662 40.7248 39.0607 39.428 39.0692C37.6595 39.0798 35.891 39.0713 34.1247 39.0713L34.1268 39.0692ZM38.354 36.2253V33.5151H29.9768V36.2253H38.354Z" fill="white"/></svg>        </span>
        <span class="medical-service-card-title">Individual Case Safety Report (ICSR) Processing and Submission</span>
      </div>
      <div class="medical-service-card-body">
        <ul class="medical-service-list">
          <li>Post-approval changes management</li>
          <li>Labeling requirements compliance</li>
          <li>Import and manufacturing licenses</li>
          <li>Registration of innovative/new medical devices</li>
          <li>Complaints management</li>
        </ul>
      </div>
    </div>
    

<!-- advantages-of-using-pharmacovigilance -->
<section class="advantages-of-using-medical-device">
  <div class="advantages-of-using-medical-device-bg-top">
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Section-Bottom-Background.png" alt="Background Top" class="medical-bg-top-img" />
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Section-Bottom-Bg-shape.png" alt="Background Corner" class="medical-bg-corner-img" />
  </div>
</section>

<section class="advantages-of-using-medical-device-section">
  <h2 class="medical-advantages-title">
    Advantages of Using Krystelis<br>Regulatory Services
  </h2>
  <div class="medical-advantages-cards">
    <div class="medical-advantage-card">
      <div class="medical-advantage-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>
      </div>
      <div class="medical-advantage-card-title">Regulatory Expertise</div>
      <div class="medical-advantage-card-desc">
        In-depth knowledge of global regulations ensures compliance with EMA, FDA, and other authorities.
      </div>
    </div>
    <div class="medical-advantage-card">
      <div class="medical-advantage-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>
      </div>
      <div class="medical-advantage-card-title">Comprehensive Services</div>
      <div class="medical-advantage-card-desc">
        End-to-end support from product conception through post-market surveillance.
      </div>
    </div>
    <div class="medical-advantage-card">
      <div class="medical-advantage-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>
      </div>
      <div class="medical-advantage-card-title">Customised Strategies</div>
      <div class="medical-advantage-card-desc">
        Tailored regulatory strategies to meet specific customer and product needs.
      </div>
    </div>
    <div class="medical-advantage-card">
      <div class="medical-advantage-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>
      </div>
      <div class="medical-advantage-card-title">Experienced Team</div>
      <div class="medical-advantage-card-desc">
        A skilled team with a proven track record in device registration and navigating regulatory pathways.
      </div>
    </div>
    <div class="medical-advantage-card">
      <div class="medical-advantage-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none"><circle cx="34" cy="34" r="34" fill="#41A4FF"></circle><path d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z" fill="white"></path><path d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z" fill="white"></path><path d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z" fill="white"></path><path d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z" fill="white"></path><path d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z" fill="white"></path><path d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z" fill="white"></path><path d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z" fill="white"></path><path d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z" fill="white"></path><path d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z" fill="white"></path><path d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z" fill="white"></path><path d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z" fill="white"></path><path d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z" fill="white"></path><path d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z" fill="white"></path><path d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z" fill="white"></path><path d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z" fill="white"></path><path d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z" fill="white"></path></svg>
      </div>
      <div class="medical-advantage-card-title">Quality Assurance</div>
      <div class="medical-advantage-card-desc">
        Commitment to maintaining high-quality standards and patient safety through robust quality systems and certifications.
      </div>
    </div>
  </div>
</section>

<!-- Get In Touch CTA Section Start -->
<section class="medical-get-in-touch-cta">
  <div class="medical-get-in-touch-bg">
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/CTA-Bg-Shape-Orange-1024x1024.png" alt="Decorative Orange Triangle" class="medical-cta-triangle medical-cta-triangle-left" />
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/CTA-Bg-Shape-Blue-1024x1024.png" alt="Decorative Blue Triangle" class="medical-cta-triangle medical-cta-triangle-right" />
    <div class="medical-get-in-touch-content">
      <div class="medical-get-in-touch-heading">INTERESTED IN LEARNING MORE? </div>
      <div class="medical-get-in-touch-title">Get In Touch</div>
      <a href="http://localhost/wordpress/contact-us/" class="medical-get-in-touch-btn">CONTACT US</a>
    </div>
  </div>
</section>

<style>
.medical-hero-wrapper {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  background: #fff;
  padding: 0;
  margin-top: 0;
  margin-bottom: 0;
}

.medical-hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 500px;
  max-width: 100%;
  margin: 0;
  padding: 0;
  gap: 0;
}

.medical-hero-text {
  padding: 40px 60px 60px 40px;
  background: #fff;
  /* display: flex; */
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  position: relative;
  opacity: 0;
  transform: translateY(40px);
  animation: revealUp 1s cubic-bezier(0.23, 1, 0.32, 1) 0.3s both;
}

@keyframes revealUp {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.medical-hero-subtitle {
  font-family: "Maven Pro", sans-serif;
  font-size: 1.75rem;
  font-weight: 500;
  color: #FF6A18;
  margin: 0 0 15px 0;
  line-height: 1.5;
  position: relative;
  overflow: hidden;
  display: inline-block;
  animation: revealDown 1s cubic-bezier(0.23, 1, 0.32, 1) both;
}

@keyframes revealDown {
  0% {
    opacity: 0;
    transform: translateY(-40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.medical-hero-title {
  font-family: "Maven Pro", sans-serif;
  font-size: 48px;
  font-weight: 700;
  color: #0072DA;
  margin: 30px 0 30px 0;
  line-height: 1.2;
}

.medical-hero-description {
  font-family: Georgia, serif;
  font-size: 20px;
  color: #525252;
  line-height: 1.5;
  margin: 0 0 20px 0;
}

.medical-hero-image {
  position: relative;
  background: #ffffff;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  /* overflow: hidden; */
  padding: 20px;
  margin-top: 7rem;
  opacity: 0;
  transform: scale(0.85);
  animation: zoomInImg 1s cubic-bezier(0.23, 1, 0.32, 1) 0.6s both;
}

@keyframes zoomInImg {
  0% {
    opacity: 0;
    transform: scale(0.85);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.medical-hero-image img {
  width: 100%;
  border-radius: 20px;
  display: block;
  margin-right: 3rem;
  object-fit: cover;
  margin-bottom: 40px;
}

/* Responsive styles */
@media (max-width: 999px) {
  .medical-hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1.5rem;
    padding: 0 10px;
    order: 2;
  }
  .medical-hero-image img {
    margin: 0 auto;
    display: block;
    border-radius: 12px;
    max-width: 100%;
    height: auto;
  }
}
@media (max-width: 999px) {
  .medical-hero-content {
    display: flex;
    flex-direction: column;
    min-height: unset;
    width: 100%;
  }
  .medical-hero-text,
  .medical-hero-image {
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
  }
  .medical-hero-image {
    margin-top: 1.5rem;
    padding: 0 10px;
    order: 2;
  }
  .medical-hero-text {
    order: 1;
    padding: 32px 16px 32px 16px;
    align-items: left;
    text-align: left;
  }
}

@media (max-width: 600px) {
  .medical-hero-text {
    padding: 18px 4vw 18px 4vw;
  }
  .medical-hero-subtitle {
    font-size: 1.25rem;
  }
  .medical-hero-title {
    font-size: 2rem;
    text-align: left;
    margin: 30px auto 10px 10px;
  }
  .medical-hero-description {
    font-size: 1rem;
    text-align: left;
    margin: 30px auto 10px 10px;
  }
  .medical-hero-image img {
    margin-right: 0;
    border-radius: 12px;
  }
}


.medical-services-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2.5rem;
  justify-content: center;
  justify-items: center;
}
.medical-service-card {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0, 114, 218, 0.10);
  flex: 1 1 420px;
  min-width: 380px;
  max-width: 700px;
  display: flex;
  flex-direction: column;
  margin: 0 0.5rem;
  opacity: 0;
  transform: translateY(-40px);
  /* No animation by default, will be added by JS */
}

.medical-service-card.medical-card-animate {
  animation: medicalCardRevealDown 0.8s cubic-bezier(0.23, 1, 0.32, 1) both;
  /* The final shadow is the same as your normal card shadow */
}

@keyframes medicalCardRevealDown {
  0% {
    opacity: 0;
    transform: translateY(-40px);
    box-shadow: 0 16px 40px rgba(65, 164, 255, 0.25), 0 0 0 rgba(255,255,255,0);
  }
  80% {
    box-shadow: 0 8px 32px rgba(65, 164, 255, 0.18), 0 0 16px 4px rgba(65, 164, 255, 0.15);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    box-shadow: 0 8px 32px rgba(0, 114, 218, 0.10);
  }
}
.medical-service-card-header {
  background: linear-gradient(180deg, #1A9CF7 0%, #0072DA 100%);
  border-radius: 18px 18px 0 0;
  padding: 1.5rem 2rem 1.5rem 2rem;
  display: flex;
  align-items: center;
  gap: 1.2rem;
}
.medical-service-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
}
.medical-service-card-title {
  font-family: "Maven Pro", sans-serif;
  font-size: 2rem;
  font-weight: 600;
  color: #fff;
  line-height: 1.2;
}
.medical-service-card-body {
  padding: 2rem 2rem 2.5rem 2rem;
}
.medical-service-list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.medical-service-list li {
  position: relative;
  padding-left: 1.8em;
  margin-bottom: 1.1em;
  font-family: Georgia, serif;
  font-size: 1.15rem;
  color: #525252;
  line-height: 1.6;
}
.medical-service-list li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.3em;
  width: 1.1em;
  height: 1.1em;
  background-image: url('data:image/svg+xml;utf8,<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7.29247 5.79247L2.20711 0.707107C1.57714 0.0771419 0.5 0.523309 0.5 1.41421V11.5849C0.5 12.4758 1.57714 12.922 2.20711 12.292L7.29247 7.20668C7.68299 6.81616 7.68299 6.18299 7.29247 5.79247Z" fill="%23FF9459"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: left center;
  display: inline-block;
}

/* Tablet: 1 column, smaller padding */
@media (max-width: 900px) {
  .medical-services-cards {
    grid-template-columns: 1fr;
    gap: 1.2rem;
    margin-left: 10px;
    margin-right: 10px;
  }
  .medical-service-card {
    max-width: 98vw;
    border-radius: 14px;
    box-shadow: 0 4px 16px rgba(0, 114, 218, 0.10);
  }
  .medical-service-card-header {
    padding: 1rem 1.2rem;
    border-radius: 14px 14px 0 0;
  }
  .medical-service-card-title {
    font-size: 1.2rem;
  }
  .medical-service-card-body {
    padding: 1.2rem 1.2rem 1.5rem 1.2rem;
  }
  .medical-service-list li {
    font-size: 1rem;
    margin-bottom: 0.7em;
    padding-left: 1.3em;
  }
}

/* Mobile: even smaller, 1 column, tighter padding */
@media (max-width: 600px) {
  .medical-services-cards-wrapper {
    padding: 0 4px;
    margin: auto 30px auto 30px;
  }
  .medical-services-cards {
    gap: 1rem;
  }
  .medical-service-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 114, 218, 0.10);
  }
  .medical-service-card-header {
    padding: 0.7rem;
    border-radius: 8px 8px 0 0;
  }
  .medical-service-card-title {
    font-size: 1rem;
  }
  .medical-service-card-body {
    padding: 0.7rem 0.7rem 1rem 0.7rem;
  }
  .medical-service-list li {
    font-size: 0.95rem;
    margin-bottom: 0.5em;
    padding-left: 1em;
  }
}

/* Mobile: even smaller, 1 column, tighter padding */
@media (max-width: 480px) {
  .medical-services-cards-wrapper {
    padding: 0 4px;
    margin: auto 30px auto 30px;
  }
  .medical-services-cards {
    gap: 1rem;
  }
  .medical-service-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 114, 218, 0.10);
  }
  .medical-service-card-header {
    padding: 0.7rem;
    border-radius: 8px 8px 0 0;
    margin-left: 40px;
    margin-right: 40px;
  }
  .medical-service-card-title {
    font-size: 1rem;
  }
  .medical-service-card-body {
    padding: 0.7rem 2.7rem 3rem 2.7rem;
  }
  .medical-service-list li {
    font-size: 0.95rem;
    margin-bottom: 0.5em;
    padding-left: 1em;
  }
}


.advantages-of-using-medical-device {
  position: relative;
  background: #fff;
  overflow: hidden;
  padding: 0 0 48px 0;
  min-height: 260px;
}
.advantages-of-using-medical-device-bg-top {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 320px;
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transform: translateY(-60px);
  animation: medicalBgTopDown 1.2s cubic-bezier(0.23, 1, 0.32, 1) both;
}
@keyframes medicalBgTopDown {
  0% {
    opacity: 0;
    transform: translateY(-60px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.medical-bg-top-img {
  width: 100%;
  height: auto;
  display: block;
}
.medical-bg-corner-img {
  position: absolute;
  top: 85px;
  right: 17vw;
  width: 180px;
  height: auto;
  z-index: -1;
  opacity: 0;
  transform: translateX(60px);
  animation: medicalBgCornerRightToLeft 1.2s cubic-bezier(0.23, 1, 0.32, 1) both;
}
@keyframes medicalBgCornerRightToLeft {
  0% {
    opacity: 0;
    transform: translateX(60px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Tablet styles (768px and below) */
@media screen and (max-width: 768px) {
  .advantages-of-using-medical-device {
    min-height: 200px;
    padding: 0 0 32px 0;
  }

  .advantages-of-using-medical-device-bg-top {
    height: 250px;
  }

  .medical-bg-corner-img {
    top: 30px;
    right: 12vw;
    width: 140px;
  }
}

/* Small tablet / Large mobile (600px and below) */
@media screen and (max-width: 600px) {
  .advantages-of-using-medical-device {
    min-height: 180px;
    padding: 0 0 24px 0;
  }

  .advantages-of-using-medical-device-bg-top {
    height: 200px;
  }

  .medical-bg-corner-img {
    top: 20px;
    right: 8vw;
    width: 120px;
  }
}

/* Mobile styles (480px and below) */
@media screen and (max-width: 480px) {
  .advantages-of-using-medical-device {
    min-height: 160px;
    padding: 0 0 20px 0;
  }

  .advantages-of-using-medical-device-bg-top {
    height: 160px;
  }

  .medical-bg-corner-img {
    top: 10px;
        right: 10vw;
        width: 100px;
  }
}

/* Small mobile (375px and below) */
@media screen and (max-width: 375px) {
  .advantages-of-using-medical-device {
    min-height: 140px;
    padding: 0 0 16px 0;
  }

  .advantages-of-using-medical-device-bg-top {
    height: 140px;
  }

  .medical-bg-corner-img {
    top: 8px;
        right: 11vw;
        width: 70px
  }
}

/* Extra small mobile (320px and below) */
@media screen and (max-width: 320px) {
  .advantages-of-using-medical-device {
    min-height: 120px;
    padding: 0 0 12px 0;
  }

  .advantages-of-using-medical-device-bg-top {
    height: 120px;
  }

  .medical-bg-corner-img {
    top: 20px;
    right: 2vw;
    width: 70px;
  }
}

/* Large screens (1200px and above) */
@media screen and (min-width: 1200px) {
  .advantages-of-using-medical-device {
    min-height: 300px;
    padding: 0 0 60px 0;
  }

  .advantages-of-using-medical-device-bg-top {
    height: 380px;
  }

  .medical-bg-corner-img {
    top: 70px;
    right: 15vw;
    width: 220px;
  }
}

/* Extra large screens (1600px and above) */
@media screen and (min-width: 1600px) {
  .advantages-of-using-medical-device {
    min-height: 350px;
    padding: 0 0 80px 0;
  }

  .advantages-of-using-medical-device-bg-top {
    height: 450px;
  }

  .medical-bg-corner-img {
    top: 120px;
    right: 12vw;
    width: 260px;
  }
}

.advantages-of-using-medical-device-section {
  max-width: 1400px;
  margin: 0 auto 40px auto;
  padding: 0 16px;
  text-align: center;
}

.medical-advantages-title {
  text-align: center;
  font-family: "Maven Pro", sans-serif;
  font-size: 3rem;
  font-weight: 700;
  color: #0072DA;
  margin-bottom: 2.5rem;
  margin-bottom: 50px;
  position: relative;
  opacity: 0;
  transform: translateY(-40px);
  animation: medicalAdvantagesTitleRevealDown 1s cubic-bezier(0.23, 1, 0.32, 1) both;
}

@keyframes medicalAdvantagesTitleRevealDown {
  0% {
    opacity: 0;
    transform: translateY(-40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.medical-advantages-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    /* max-width: 1400px; */
    margin: 0 auto;
    /* padding: 0 40px; */
    align-items: stretch;
}

.medical-advantage-card {
    background: #fff;
    border-radius: 24px;
    border: 1px solid #eaeaea;
    box-shadow: 0 6px 32px rgba(65, 164, 255, 0.13);
    font-family: Georgia, serif;
    font-size: 1.6rem;
    color: #525252;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 36px 36px 36px;
    margin: 0;
    font-weight: 400;
    transition: box-shadow 0.2s;
    text-align: center;
    position: relative;
    height: 100%;
    box-sizing: border-box;
}

.medical-advantage-card:hover {
    box-shadow: 0px 0px 25px 0px rgba(65, 164, 255, 0.3137);

  }

.medical-advantage-icon {
    position: absolute;
    top: -32px;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 2px 12px rgba(65, 164, 255, 0.10);
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.medical-advantage-card-title {
  color: #0072DA;
  font-size: 1.50rem;
  font-family: 'Maven Pro', sans-serif;
  font-weight: 500;
  margin-top: 36px;
  margin-bottom: 12px;
}

.medical-advantage-card-desc {
    /* margin-top: 32px; */
    font-size: 1.35rem;
    color: #525252;
    font-family: Georgia, serif;
    line-height: 1.5;
}

.medical-partner-footer {
  color: #FF6A18;
  font-size: 1.50rem;
  font-family: 'Maven Pro', sans-serif;
  font-weight: 500;
  margin-top: 32px;
  letter-spacing: 1px;
}

/* Responsive */
@media (max-width: 1100px) {
  .medical-advantages-cards {
    grid-template-columns: 1fr 1fr;
    gap: 28px 20px;
  }
}
@media (max-width: 700px) {
  .medical-advantages-title {
    font-size: 1.5rem;
    margin-bottom: 18px;
  }
  .medical-advantages-cards {
    grid-template-columns: 1fr;
    gap: 60px 0;
  }
  .medical-advantage-card {
    padding: 28px 10px 24px 10px;
    min-height: 120px;
  }
  .medical-advantage-card-title {
    font-size: 1.1rem;
    margin-top: 28px;
  }
  .medical-partner-footer {
    font-size: 1rem;
    margin-top: 18px;
  }
}

.medical-get-in-touch-cta {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 64px 0 32px 0;
  background: #fff;
  position: relative;
  z-index: 1;
}
.medical-get-in-touch-bg {
  position: relative;
  background: linear-gradient(180deg, #41A4FF 0%, #0072DA 100%);
  border-radius: 36px;
  width: 80vw;
  max-width: 1000px;
  min-height: 300px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 40px rgba(65,164,255,0.10);
  overflow: visible;
}
.medical-get-in-touch-content {
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 56px 24px 48px 24px;
  z-index: 2;
}
.medical-get-in-touch-heading {
  color: #fff;
  font-size: 1.50rem;
  font-family: Georgia, serif;
  font-weight: 500;
  text-align: center;
  margin-bottom: 32px;
  letter-spacing: 12px;
}
.medical-get-in-touch-title {
  color: #fff;
  font-size: 3.2rem;
  font-family: 'Maven Pro', sans-serif;
  font-weight: 700;
  text-align: center;
  margin-bottom: 36px;
  margin-top: 1px;
}
.medical-get-in-touch-btn {
    display: inline-block;
    background: white;
    color:rgb(71, 67, 67);
    padding: 1.5rem 2rem;
    border-radius: 15px;
    text-decoration: none;
    font-weight: 500;
    font-size: 1.3rem;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    box-shadow: 0 8px 20px 0072DA;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 10px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}
.medical-get-in-touch-btn:hover {
    background: #ffffff;
    color: #0072DA;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 136, 255, 0.4); /* Brighter effect */
    text-decoration: none;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 20px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}
.medical-cta-triangle {
  position: absolute;
  z-index: 1;
  width: 180px;
  height: auto;
  pointer-events: none;
}
.medical-cta-triangle-left {
  left: -120px;
  bottom: 40px;
  z-index: -1;
}
.medical-cta-triangle-right {
  right: -100px;
  top: 40px;
}
@media (max-width: 900px) {
  .medical-get-in-touch-bg {
    width: 96vw;
    min-height: 260px;
    border-radius: 24px;
  }
  .medical-get-in-touch-title {
    font-size: 2.2rem;
  }
  .medical-get-in-touch-heading {
    font-size: 1.2rem;
  }
  .medical-cta-triangle {
    width: 100px;
  }
  .medical-cta-triangle-left {
    left: -60px;
    bottom: 10px;
  }
  .medical-cta-triangle-right {
    right: -40px;
    top: 10px;
  }
}
@media (max-width: 600px) {
  .medical-get-in-touch-bg {
    min-height: 180px;
    padding: 0;
  }
  .medical-get-in-touch-content {
    padding: 24px 4vw 24px 4vw;
  }
  .medical-get-in-touch-title {
    font-size: 1.3rem;
    margin-bottom: 18px;
  }
  .medical-get-in-touch-heading {
    font-size: 0.95rem;
    margin-bottom: 18px;
  }
  .medical-get-in-touch-btn {
    font-size: 1.1rem;
    padding: 12px 24px;
    border-radius: 12px;
  }
  .medical-cta-triangle {
    width: 48px;
  }
  .medical-cta-triangle-left {
    left: -18px;
    bottom: 0px;
  }
  .medical-cta-triangle-right {
    right: -12px;
    top: 0px;
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function () {
  function moveMedicalHeroImageResponsive() {
    var heroText = document.querySelector('.medical-hero-text');
    var heroImage = document.querySelector('.medical-hero-image');
    var heroSubtitle = document.querySelector('.medical-hero-subtitle');
    var heroContent = document.querySelector('.medical-hero-content');

    if (window.innerWidth <= 999) {
      // Mobile: Move image after subtitle
      if (heroText && heroImage && heroSubtitle) {
        var nextSibling = heroSubtitle.nextElementSibling;
        if (nextSibling !== heroImage) {
          if (heroImage.parentNode) {
            heroImage.parentNode.removeChild(heroImage);
          }
          heroSubtitle.parentNode.insertBefore(heroImage, heroSubtitle.nextSibling);
        }
      }
    } else {
      // Desktop: Move image back to original position
      if (heroContent && heroImage) {
        var lastChild = heroContent.lastElementChild;
        if (lastChild !== heroImage) {
          if (heroImage.parentNode) {
            heroImage.parentNode.removeChild(heroImage);
          }
          heroContent.appendChild(heroImage);
        }
      }
    }
  }
  moveMedicalHeroImageResponsive();
  window.addEventListener('resize', moveMedicalHeroImageResponsive);
});


document.addEventListener('DOMContentLoaded', function () {
  var subtitle = document.querySelector('.medical-hero-subtitle');
  if (!subtitle) return;

  // Helper to trigger animation
  function triggerAnimation() {
    subtitle.style.animation = 'none';
    // Force reflow
    void subtitle.offsetWidth;
    subtitle.style.animation = 'revealDown 1s cubic-bezier(0.23, 1, 0.32, 1) both';
  }

  // Initial animation (in case not already triggered)
  triggerAnimation();

  // Intersection Observer for scroll into view
  var observer = new window.IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        triggerAnimation();
      }
    });
  }, { threshold: 0.5 });

  observer.observe(subtitle);
});


document.addEventListener('DOMContentLoaded', function () {
  var heroImage = document.querySelector('.medical-hero-image');
  if (!heroImage) return;

  function triggerZoomAnimation() {
    heroImage.style.animation = 'none';
    void heroImage.offsetWidth; // Force reflow
    heroImage.style.animation = 'zoomInImg 1s cubic-bezier(0.23, 1, 0.32, 1) 0.6s both';
  }

  // Initial animation
  triggerZoomAnimation();

  // Intersection Observer for scroll into view
  var observer = new window.IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        triggerZoomAnimation();
      }
    });
  }, { threshold: 0.5 });

  observer.observe(heroImage);
});

document.addEventListener('DOMContentLoaded', function () {
  var cards = document.querySelectorAll('.medical-service-card');
  if (!cards.length) return;

  var observer = new IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        // Staggered animation: delay based on card index
        var index = Array.from(cards).indexOf(entry.target);
        setTimeout(function() {
          entry.target.classList.add('medical-card-animate');
        }, index * 200); // 200ms delay between each card
        observer.unobserve(entry.target); // Animate only once
      }
    });
  }, { threshold: 0.3 });

  cards.forEach(function(card) {
    observer.observe(card);
  });
});

document.addEventListener('DOMContentLoaded', function () {
  var bgImg = document.querySelector('.medical-bg-top-img');
  if (!bgImg) return;

  function triggerBgAnimation() {
    bgImg.style.animation = 'none';
    void bgImg.offsetWidth; // Force reflow
    bgImg.style.animation = 'medicalBgTopDown 1.2s cubic-bezier(0.23, 1, 0.32, 1) both';
  }

  // Initial animation
  triggerBgAnimation();

  // Intersection Observer for scroll into view
  var observer = new window.IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        triggerBgAnimation();
      }
    });
  }, { threshold: 0.3 });

  observer.observe(bgImg);
});

document.addEventListener('DOMContentLoaded', function () {
  var cornerImg = document.querySelector('.medical-bg-corner-img');
  if (!cornerImg) return;

  function triggerCornerAnimation() {
    cornerImg.style.animation = 'none';
    void cornerImg.offsetWidth; // Force reflow
    cornerImg.style.animation = 'medicalBgCornerRightToLeft 1.2s cubic-bezier(0.23, 1, 0.32, 1) both';
  }

  // Initial animation
  triggerCornerAnimation();

  // Intersection Observer for scroll into view
  var observer = new window.IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        triggerCornerAnimation();
      }
    });
  }, { threshold: 0.3 });

  observer.observe(cornerImg);
});

document.addEventListener('DOMContentLoaded', function () {
  var advTitle = document.querySelector('.medical-advantages-title');
  if (!advTitle) return;

  function triggerAdvTitleAnimation() {
    advTitle.style.animation = 'none';
    void advTitle.offsetWidth;
    advTitle.style.animation = 'medicalAdvantagesTitleRevealDown 1s cubic-bezier(0.23, 1, 0.32, 1) both';
  }

  // Initial animation
  triggerAdvTitleAnimation();

  // Intersection Observer for scroll into view
  var observer = new window.IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        triggerAdvTitleAnimation();
      }
    });
  }, { threshold: 0.5 });

  observer.observe(advTitle);
});
</script>


<style>
.pharma-hero-wrapper {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  background: #fff;
  padding: 0;
  margin-top: 0;
  margin-bottom: 0;
}

.pharma-hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 500px;
  max-width: 100%;
  margin: 0;
  padding: 0;
  gap: 0;
}

.pharma-hero-text {
  padding: 40px 60px 60px 40px;
  background: #fff;
  /* display: flex; */
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  position: relative;
  opacity: 0;
  transform: translateY(40px);
  animation: revealUp 1s cubic-bezier(0.23, 1, 0.32, 1) 0.3s both;
}

@keyframes revealUp {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.pharma-hero-subtitle {
  font-family: "Maven Pro", sans-serif;
  font-size: 1.75rem;
  font-weight: 500;
  color: #FF6A18;
  margin: 0 0 15px 0;
  line-height: 1.5;
  position: relative;
  overflow: hidden;
  display: inline-block;
  animation: revealDown 1s cubic-bezier(0.23, 1, 0.32, 1) both;
}

@keyframes revealDown {
  0% {
    opacity: 0;
    transform: translateY(-40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.pharma-hero-title {
  font-family: "Maven Pro", sans-serif;
  font-size: 48px;
  font-weight: 700;
  color: #0072DA;
  margin: 30px 0 30px 0;
  line-height: 1.2;
}

.pharma-hero-description {
  font-family: Georgia, serif;
  font-size: 20px;
  color: #525252;
  line-height: 1.5;
  margin: 0 0 20px 0;
}

.pharma-hero-image {
  position: relative;
  background: #ffffff;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  /* overflow: hidden; */
  padding: 20px;
  margin-top: 7rem;
  opacity: 0;
  transform: scale(0.85);
  animation: zoomInImg 1s cubic-bezier(0.23, 1, 0.32, 1) 0.6s both;
}

@keyframes zoomInImg {
  0% {
    opacity: 0;
    transform: scale(0.85);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.pharma-hero-image img {
  width: 100%;
  border-radius: 20px;
  display: block;
  margin-right: 3rem;
  object-fit: cover;
  margin-bottom: 40px;
}

/* Responsive styles */
@media (max-width: 999px) {
  .pharma-hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1.5rem;
    padding: 0 10px;
    order: 2;
  }
  .pharma-hero-image img {
    margin: 0 auto;
    display: block;
    border-radius: 12px;
    max-width: 100%;
    height: auto;
  }
}
@media (max-width: 999px) {
  .pharma-hero-content {
    display: flex;
    flex-direction: column;
    min-height: unset;
    width: 100%;
  }
  .pharma-hero-text,
  .pharma-hero-image {
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
  }
  .pharma-hero-image {
    margin-top: 1.5rem;
    padding: 0 10px;
    order: 2;
  }
  .pharma-hero-text {
    order: 1;
    padding: 32px 16px 32px 16px;
    align-items: left;
    text-align: left;
  }
}

@media (max-width: 600px) {
  .pharma-hero-text {
    padding: 18px 4vw 18px 4vw;
  }
  .pharma-hero-subtitle {
    font-size: 1.25rem;
  }
  .pharma-hero-title {
    font-size: 2rem;
    text-align: left;
    margin: 30px auto 10px 10px;
  }
  .pharma-hero-description {
    font-size: 1rem;
    text-align: left;
    margin: 30px auto 10px 10px;
  }
  .pharma-hero-image img {
    margin-right: 0;
    border-radius: 12px;
  }
}


.pharma-services-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2.5rem;
  justify-content: center;
  justify-items: center;
}
.pharma-service-card {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(0, 114, 218, 0.10);
  flex: 1 1 420px;
  min-width: 380px;
  max-width: 700px;
  display: flex;
  flex-direction: column;
  margin: 0 0.5rem;
  opacity: 0;
  transform: translateY(-40px);
  /* No animation by default, will be added by JS */
}

.pharma-service-card.pharma-card-animate {
  animation: pharmaCardRevealDown 0.8s cubic-bezier(0.23, 1, 0.32, 1) both;
  /* The final shadow is the same as your normal card shadow */
}

@keyframes pharmaCardRevealDown {
  0% {
    opacity: 0;
    transform: translateY(-40px);
    box-shadow: 0 16px 40px rgba(65, 164, 255, 0.25), 0 0 0 rgba(255,255,255,0);
  }
  80% {
    box-shadow: 0 8px 32px rgba(65, 164, 255, 0.18), 0 0 16px 4px rgba(65, 164, 255, 0.15);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    box-shadow: 0 8px 32px rgba(0, 114, 218, 0.10);
  }
}
.pharma-service-card-header {
  background: linear-gradient(180deg, #1A9CF7 0%, #0072DA 100%);
  border-radius: 18px 18px 0 0;
  padding: 1.5rem 2rem 1.5rem 2rem;
  display: flex;
  align-items: center;
  gap: 1.2rem;
}
.pharma-service-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
}
.pharma-service-card-title {
  font-family: "Maven Pro", sans-serif;
  font-size: 2rem;
  font-weight: 600;
  color: #fff;
  line-height: 1.2;
}
.pharma-service-card-body {
  padding: 2rem 2rem 2.5rem 2rem;
}
.pharma-service-list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.pharma-service-list li {
  position: relative;
  padding-left: 1.8em;
  margin-bottom: 1.1em;
  font-family: Georgia, serif;
  font-size: 1.15rem;
  color: #525252;
  line-height: 1.6;
}
.pharma-service-list li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.3em;
  width: 1.1em;
  height: 1.1em;
  background-image: url('data:image/svg+xml;utf8,<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7.29247 5.79247L2.20711 0.707107C1.57714 0.0771419 0.5 0.523309 0.5 1.41421V11.5849C0.5 12.4758 1.57714 12.922 2.20711 12.292L7.29247 7.20668C7.68299 6.81616 7.68299 6.18299 7.29247 5.79247Z" fill="%23FF9459"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: left center;
  display: inline-block;
}

/* Tablet: 1 column, smaller padding */
@media (max-width: 900px) {
  .pharma-services-cards {
    grid-template-columns: 1fr;
    gap: 1.2rem;
    margin-left: 10px;
    margin-right: 10px;
  }
  .pharma-service-card {
    max-width: 98vw;
    border-radius: 14px;
    box-shadow: 0 4px 16px rgba(0, 114, 218, 0.10);
  }
  .pharma-service-card-header {
    padding: 1rem 1.2rem;
    border-radius: 14px 14px 0 0;
  }
  .pharma-service-card-title {
    font-size: 1.2rem;
  }
  .pharma-service-card-body {
    padding: 1.2rem 1.2rem 1.5rem 1.2rem;
  }
  .pharma-service-list li {
    font-size: 1rem;
    margin-bottom: 0.7em;
    padding-left: 1.3em;
  }
}

/* Mobile: even smaller, 1 column, tighter padding */
@media (max-width: 600px) {
  .pharma-services-cards-wrapper {
    padding: 0 4px;
    margin: auto 30px auto 30px;
  }
  .pharma-services-cards {
    gap: 1rem;
  }
  .pharma-service-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 114, 218, 0.10);
  }
  .pharma-service-card-header {
    padding: 0.7rem;
    border-radius: 8px 8px 0 0;
  }
  .pharma-service-card-title {
    font-size: 1rem;
  }
  .pharma-service-card-body {
    padding: 0.7rem 0.7rem 1rem 0.7rem;
  }
  .pharma-service-list li {
    font-size: 0.95rem;
    margin-bottom: 0.5em;
    padding-left: 1em;
  }
}

/* Mobile: even smaller, 1 column, tighter padding */
@media (max-width: 480px) {
  .pharma-services-cards-wrapper {
    padding: 0 4px;
    margin: auto 30px auto 30px;
  }
  .pharma-services-cards {
    gap: 1rem;
  }
  .pharma-service-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 114, 218, 0.10);
  }
  .pharma-service-card-header {
    padding: 0.7rem;
    border-radius: 8px 8px 0 0;
    margin-left: 40px;
    margin-right: 40px;
  }
  .pharma-service-card-title {
    font-size: 1rem;
  }
  .pharma-service-card-body {
    padding: 0.7rem 2.7rem 3rem 2.7rem;
  }
  .pharma-service-list li {
    font-size: 0.95rem;
    margin-bottom: 0.5em;
    padding-left: 1em;
  }
}


.advantages-of-using-pharmacovigilance {
  position: relative;
  background: #fff;
  overflow: hidden;
  padding: 0 0 48px 0;
  min-height: 260px;
}
.advantages-of-using-pharmacovigilance-bg-top {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 320px;
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transform: translateY(-60px);
  animation: pharmaBgTopDown 1.2s cubic-bezier(0.23, 1, 0.32, 1) both;
}
@keyframes pharmaBgTopDown {
  0% {
    opacity: 0;
    transform: translateY(-60px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.pharma-bg-top-img {
  width: 100%;
  height: auto;
  display: block;
}
.pharma-bg-corner-img {
  position: absolute;
  top: 85px;
  right: 17vw;
  width: 180px;
  height: auto;
  z-index: -1;
  opacity: 0;
  transform: translateX(60px);
  animation: pharmaBgCornerRightToLeft 1.2s cubic-bezier(0.23, 1, 0.32, 1) both;
}
@keyframes pharmaBgCornerRightToLeft {
  0% {
    opacity: 0;
    transform: translateX(60px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Tablet styles (768px and below) */
@media screen and (max-width: 768px) {
  .advantages-of-using-pharmacovigilance {
    min-height: 200px;
    padding: 0 0 32px 0;
  }

  .advantages-of-using-pharmacovigilance-bg-top {
    height: 250px;
  }

  .pharma-bg-corner-img {
    top: 30px;
    right: 12vw;
    width: 140px;
  }
}

/* Small tablet / Large mobile (600px and below) */
@media screen and (max-width: 600px) {
  .advantages-of-using-pharmacovigilance {
    min-height: 180px;
    padding: 0 0 24px 0;
  }

  .advantages-of-using-pharmacovigilance-bg-top {
    height: 200px;
  }

  .pharma-bg-corner-img {
    top: 20px;
    right: 8vw;
    width: 120px;
  }
}

/* Mobile styles (480px and below) */
@media screen and (max-width: 480px) {
  .advantages-of-using-pharmacovigilance {
    min-height: 160px;
    padding: 0 0 20px 0;
  }

  .advantages-of-using-pharmacovigilance-bg-top {
    height: 160px;
  }

  .pharma-bg-corner-img {
    top: 10px;
        right: 10vw;
        width: 100px;
  }
}

/* Small mobile (375px and below) */
@media screen and (max-width: 375px) {
  .advantages-of-using-pharmacovigilance {
    min-height: 140px;
    padding: 0 0 16px 0;
  }

  .advantages-of-using-pharmacovigilance-bg-top {
    height: 140px;
  }

  .pharma-bg-corner-img {
    top: 8px;
        right: 11vw;
        width: 70px
  }
}

/* Extra small mobile (320px and below) */
@media screen and (max-width: 320px) {
  .advantages-of-using-pharmacovigilance {
    min-height: 120px;
    padding: 0 0 12px 0;
  }

  .advantages-of-using-pharmacovigilance-bg-top {
    height: 120px;
  }

  .pharma-bg-corner-img {
    top: 20px;
    right: 2vw;
    width: 70px;
  }
}

/* Large screens (1200px and above) */
@media screen and (min-width: 1200px) {
  .advantages-of-using-pharmacovigilance {
    min-height: 300px;
    padding: 0 0 60px 0;
  }

  .advantages-of-using-pharmacovigilance-bg-top {
    height: 380px;
  }

  .pharma-bg-corner-img {
    top: 70px;
    right: 15vw;
    width: 220px;
  }
}

/* Extra large screens (1600px and above) */
@media screen and (min-width: 1600px) {
  .advantages-of-using-pharmacovigilance {
    min-height: 350px;
    padding: 0 0 80px 0;
  }

  .advantages-of-using-pharmacovigilance-bg-top {
    height: 450px;
  }

  .pharma-bg-corner-img {
    top: 120px;
    right: 12vw;
    width: 260px;
  }
}

.advantages-of-using-pharmacovigilance-section {
  max-width: 1400px;
  margin: 0 auto 40px auto;
  padding: 0 16px;
  text-align: center;
}

.pharma-advantages-title {
  text-align: center;
  font-family: "Maven Pro", sans-serif;
  font-size: 3rem;
  font-weight: 700;
  color: #0072DA;
  margin-bottom: 2.5rem;
  margin-bottom: 50px;
  position: relative;
  opacity: 0;
  transform: translateY(-40px);
  animation: pharmaAdvantagesTitleRevealDown 1s cubic-bezier(0.23, 1, 0.32, 1) both;
}

@keyframes pharmaAdvantagesTitleRevealDown {
  0% {
    opacity: 0;
    transform: translateY(-40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.pharma-advantages-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    /* max-width: 1400px; */
    margin: 0 auto;
    /* padding: 0 40px; */
    align-items: stretch;
}

.pharma-advantage-card {
    background: #fff;
    border-radius: 24px;
    border: 1px solid #eaeaea;
    box-shadow: 0 6px 32px rgba(65, 164, 255, 0.13);
    font-family: Georgia, serif;
    font-size: 1.6rem;
    color: #525252;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 36px 36px 36px;
    margin: 0;
    font-weight: 400;
    transition: box-shadow 0.2s;
    text-align: center;
    position: relative;
    height: 100%;
    box-sizing: border-box;
}

.pharma-advantage-card:hover {
    box-shadow: 0px 0px 25px 0px rgba(65, 164, 255, 0.3137);

  }

.pharma-advantage-icon {
    position: absolute;
    top: -32px;
    left: 50%;
    transform: translateX(-50%);
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 2px 12px rgba(65, 164, 255, 0.10);
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}

.pharma-advantage-card-title {
  color: #0072DA;
  font-size: 1.50rem;
  font-family: 'Maven Pro', sans-serif;
  font-weight: 500;
  margin-top: 36px;
  margin-bottom: 12px;
}

.pharma-advantage-card-desc {
    /* margin-top: 32px; */
    font-size: 1.35rem;
    color: #525252;
    font-family: Georgia, serif;
    line-height: 1.5;
}

.Partner-footer {
  color: #FF6A18;
  font-size: 1.50rem;
  font-family: 'Maven Pro', sans-serif;
  font-weight: 500;
  margin-top: 32px;
  letter-spacing: 1px;
}

/* Responsive */
@media (max-width: 1100px) {
  .pharma-advantages-cards {
    grid-template-columns: 1fr 1fr;
    gap: 28px 20px;
  }
}
@media (max-width: 700px) {
  .pharma-advantages-title {
    font-size: 1.5rem;
    margin-bottom: 18px;
  }
  .pharma-advantages-cards {
    grid-template-columns: 1fr;
    gap: 60px 0;
  }
  .pharma-advantage-card {
    padding: 28px 10px 24px 10px;
    min-height: 120px;
  }
  .pharma-advantage-card-title {
    font-size: 1.1rem;
    margin-top: 28px;
  }
  .Partner-footer {
    font-size: 1rem;
    margin-top: 18px;
  }
}

.pharma-get-in-touch-cta {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 64px 0 32px 0;
  background: #fff;
  position: relative;
  z-index: 1;
}
.pharma-get-in-touch-bg {
  position: relative;
  background: linear-gradient(180deg, #41A4FF 0%, #0072DA 100%);
  border-radius: 36px;
  width: 80vw;
  max-width: 1000px;
  min-height: 300px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 40px rgba(65,164,255,0.10);
  overflow: visible;
}
.pharma-get-in-touch-content {
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 56px 24px 48px 24px;
  z-index: 2;
}
.pharma-get-in-touch-heading {
  color: #fff;
  font-size: 1.50rem;
  font-family: Georgia, serif;
  font-weight: 500;
  text-align: center;
  margin-bottom: 32px;
  letter-spacing: 12px;
}
.pharma-get-in-touch-title {
  color: #fff;
  font-size: 3.2rem;
  font-family: 'Maven Pro', sans-serif;
  font-weight: 700;
  text-align: center;
  margin-bottom: 36px;
  margin-top: 1px;
}
.pharma-get-in-touch-btn {
    display: inline-block;
    background: white;
    color:rgb(71, 67, 67);
    padding: 1.5rem 2rem;
    border-radius: 15px;
    text-decoration: none;
    font-weight: 500;
    font-size: 1.3rem;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    box-shadow: 0 8px 20px 0072DA;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 10px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}
.pharma-get-in-touch-btn:hover {
    background: #ffffff;
    color: #0072DA;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 136, 255, 0.4); /* Brighter effect */
    text-decoration: none;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 20px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}
.pharma-cta-triangle {
  position: absolute;
  z-index: 1;
  width: 180px;
  height: auto;
  pointer-events: none;
}
.pharma-cta-triangle-left {
  left: -120px;
  bottom: 40px;
  z-index: -1;
}
.pharma-cta-triangle-right {
  right: -100px;
  top: 40px;
}
@media (max-width: 900px) {
  .pharma-get-in-touch-bg {
    width: 96vw;
    min-height: 260px;
    border-radius: 24px;
  }
  .pharma-get-in-touch-title {
    font-size: 2.2rem;
  }
  .pharma-get-in-touch-heading {
    font-size: 1.2rem;
  }
  .pharma-cta-triangle {
    width: 100px;
  }
  .pharma-cta-triangle-left {
    left: -60px;
    bottom: 10px;
  }
  .pharma-cta-triangle-right {
    right: -40px;
    top: 10px;
  }
}
@media (max-width: 600px) {
  .pharma-get-in-touch-bg {
    min-height: 180px;
    padding: 0;
  }
  .pharma-get-in-touch-content {
    padding: 24px 4vw 24px 4vw;
  }
  .pharma-get-in-touch-title {
    font-size: 1.3rem;
    margin-bottom: 18px;
  }
  .pharma-get-in-touch-heading {
    font-size: 0.95rem;
    margin-bottom: 18px;
  }
  .pharma-get-in-touch-btn {
    font-size: 1.1rem;
    padding: 12px 24px;
    border-radius: 12px;
  }
  .pharma-cta-triangle {
    width: 48px;
  }
  .pharma-cta-triangle-left {
    left: -18px;
    bottom: 0px;
  }
  .pharma-cta-triangle-right {
    right: -12px;
    top: 0px;
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function () {
  function movePharmaHeroImageResponsive() {
    var heroText = document.querySelector('.pharma-hero-text');
    var heroImage = document.querySelector('.pharma-hero-image');
    var heroSubtitle = document.querySelector('.pharma-hero-subtitle');
    var heroContent = document.querySelector('.pharma-hero-content');

    if (window.innerWidth <= 999) {
      // Mobile: Move image after subtitle
      if (heroText && heroImage && heroSubtitle) {
        var nextSibling = heroSubtitle.nextElementSibling;
        if (nextSibling !== heroImage) {
          if (heroImage.parentNode) {
            heroImage.parentNode.removeChild(heroImage);
          }
          heroSubtitle.parentNode.insertBefore(heroImage, heroSubtitle.nextSibling);
        }
      }
    } else {
      // Desktop: Move image back to original position
      if (heroContent && heroImage) {
        var lastChild = heroContent.lastElementChild;
        if (lastChild !== heroImage) {
          if (heroImage.parentNode) {
            heroImage.parentNode.removeChild(heroImage);
          }
          heroContent.appendChild(heroImage);
        }
      }
    }
  }
  movePharmaHeroImageResponsive();
  window.addEventListener('resize', movePharmaHeroImageResponsive);
});


document.addEventListener('DOMContentLoaded', function () {
  var subtitle = document.querySelector('.pharma-hero-subtitle');
  if (!subtitle) return;

  // Helper to trigger animation
  function triggerAnimation() {
    subtitle.style.animation = 'none';
    // Force reflow
    void subtitle.offsetWidth;
    subtitle.style.animation = 'revealDown 1s cubic-bezier(0.23, 1, 0.32, 1) both';
  }

  // Initial animation (in case not already triggered)
  triggerAnimation();

  // Intersection Observer for scroll into view
  var observer = new window.IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        triggerAnimation();
      }
    });
  }, { threshold: 0.5 });

  observer.observe(subtitle);
});


document.addEventListener('DOMContentLoaded', function () {
  var heroImage = document.querySelector('.pharma-hero-image');
  if (!heroImage) return;

  function triggerZoomAnimation() {
    heroImage.style.animation = 'none';
    void heroImage.offsetWidth; // Force reflow
    heroImage.style.animation = 'zoomInImg 1s cubic-bezier(0.23, 1, 0.32, 1) 0.6s both';
  }

  // Initial animation
  triggerZoomAnimation();

  // Intersection Observer for scroll into view
  var observer = new window.IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        triggerZoomAnimation();
      }
    });
  }, { threshold: 0.5 });

  observer.observe(heroImage);
});

document.addEventListener('DOMContentLoaded', function () {
  var cards = document.querySelectorAll('.pharma-service-card');
  if (!cards.length) return;

  var observer = new IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        // Staggered animation: delay based on card index
        var index = Array.from(cards).indexOf(entry.target);
        setTimeout(function() {
          entry.target.classList.add('pharma-card-animate');
        }, index * 200); // 200ms delay between each card
        observer.unobserve(entry.target); // Animate only once
      }
    });
  }, { threshold: 0.3 });

  cards.forEach(function(card) {
    observer.observe(card);
  });
});

document.addEventListener('DOMContentLoaded', function () {
  var bgImg = document.querySelector('.pharma-bg-top-img');
  if (!bgImg) return;

  function triggerBgAnimation() {
    bgImg.style.animation = 'none';
    void bgImg.offsetWidth; // Force reflow
    bgImg.style.animation = 'pharmaBgTopDown 1.2s cubic-bezier(0.23, 1, 0.32, 1) both';
  }

  // Initial animation
  triggerBgAnimation();

  // Intersection Observer for scroll into view
  var observer = new window.IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        triggerBgAnimation();
      }
    });
  }, { threshold: 0.3 });

  observer.observe(bgImg);
});

document.addEventListener('DOMContentLoaded', function () {
  var cornerImg = document.querySelector('.pharma-bg-corner-img');
  if (!cornerImg) return;

  function triggerCornerAnimation() {
    cornerImg.style.animation = 'none';
    void cornerImg.offsetWidth; // Force reflow
    cornerImg.style.animation = 'pharmaBgCornerRightToLeft 1.2s cubic-bezier(0.23, 1, 0.32, 1) both';
  }

  // Initial animation
  triggerCornerAnimation();

  // Intersection Observer for scroll into view
  var observer = new window.IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        triggerCornerAnimation();
      }
    });
  }, { threshold: 0.3 });

  observer.observe(cornerImg);
});

document.addEventListener('DOMContentLoaded', function () {
  var advTitle = document.querySelector('.pharma-advantages-title');
  if (!advTitle) return;

  function triggerAdvTitleAnimation() {
    advTitle.style.animation = 'none';
    void advTitle.offsetWidth;
    advTitle.style.animation = 'pharmaAdvantagesTitleRevealDown 1s cubic-bezier(0.23, 1, 0.32, 1) both';
  }

  // Initial animation
  triggerAdvTitleAnimation();

  // Intersection Observer for scroll into view
  var observer = new window.IntersectionObserver(function(entries) {
    entries.forEach(function(entry) {
      if (entry.isIntersecting) {
        triggerAdvTitleAnimation();
      }
    });
  }, { threshold: 0.5 });

  observer.observe(advTitle);
});
</script>

<?php get_footer(); ?>  