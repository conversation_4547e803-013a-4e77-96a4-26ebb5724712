<?php
/**
 * Template for Insights Page (Blog/News)
 *
 * @package Krystelis_Custom
 */

get_header();
?>

<!-- Mobile Research Clarity Section -->
<div class="mobile-research-clarity">
    <h3>Making clinical research crystal clear</h3>
</div>

<!-- Insights Hero Section -->
<div class="insights-hero-wrapper">
    <div class="insights-hero-content">
        <div class="hero-text">
            <h3 class="hero-subtitle">Making clinical research crystal clear</h3>
            <h1 class="hero-title">Insights centre</h1>
            <p class="hero-description">
                Krystelis colleagues are leaders in their areas of expertise, and our Insights Centre gives you access to a treasure trove of their perspectives. These bring clarity to evolving market dynamics and changing regulations, and offer practical solutions to help you achieve your goals.
            </p>
            <p class="hero-description">
                Please reach out to our global experts for actionable insights that will help you succeed in a constantly changing environment.
            </p>
            <a href="http://localhost/wordpress/contact-us/" class="hero-btn">CONTACT US</a>
        </div>
        <div class="hero-image">
            <?php if (has_post_thumbnail()) : ?>
                <?php the_post_thumbnail('large'); ?>
            <?php else : ?>
                <?php
                // Fallback images array
                $fallback_images = array(
                    get_template_directory_uri() . '/assets/images/Krystelis-Insights-Centre-large-Image.png',
                );
                // Pick a random fallback image
                $random_fallback = $fallback_images[array_rand($fallback_images)];
                ?>
                <img src="<?php echo esc_url($random_fallback); ?>" alt="No Image Available" />
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Articles Section -->
<div class="articles-section">
    <div class="articles-container">
        <h2 class="articles-title">Articles</h2>
        <p class="articles-subtitle">Find the latest articles from our experts on topics that matter</p>

        <div class="articles-grid articles-grid-five">
            <?php
            // Query latest 5 posts (customize as needed)
            $args = array(
                'post_type' => 'post',
                'posts_per_page' => 6,
            );
            $insights_query = new WP_Query($args);

            if ($insights_query->have_posts()) :
                while ($insights_query->have_posts()) : $insights_query->the_post();
                    $categories = get_the_category();
                    ?>
                    <div class="article-box">
                        <a href="<?php the_permalink(); ?>" style="text-decoration:none; color:inherit;">
                            <div class="article-image">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('large'); ?>
                                <?php else : ?>
                                    <?php
                                    // Fallback images array
                                    $fallback_images = array(
                                        get_template_directory_uri() . '/assets/images/Untitled-design-1-637x478.png',
                                        get_template_directory_uri() . '/assets/images/Untitled-design-3-637x478.png',
                                        get_template_directory_uri() . '/assets/images/Untitled-design-637x478.png',
                                        get_template_directory_uri() . '/assets/images/AI-in-SLR_Hero-image-637x478.png',
                                    );
                                    // Pick a random fallback image
                                    $random_fallback = $fallback_images[array_rand($fallback_images)];
                                    ?>
                                    <img src="<?php echo esc_url($random_fallback); ?>" alt="No Image Available" />
                                <?php endif; ?>
                            </div>
                            <div class="article-overlay">
                                <div class="article-date"><?php echo get_the_date('F j, Y'); ?></div>
                                <h3 class="article-title"><?php the_title(); ?></h3>
                                <?php if (!empty($categories)) : ?>
                                    <div class="article-categories">
                                        <?php foreach ($categories as $cat) : ?>
                                            <!-- <span class="article-category"><?php echo esc_html($cat->name); ?></span> -->
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </a>
                    </div>
                    <?php
                endwhile;
                wp_reset_postdata();
            else :
                echo '<p>No articles found.</p>';
            endif;
            ?>
        </div>

        <!-- Read More Button -->
        <div class="read-more-container">
        <a href="http://localhost/wordpress/articles/" class="read-more-btn">READ MORE</a>
        </div>
    </div>
</div>

<!-- Quarterly EU-CTR Forum Section -->
<section class="forum-section">
    <div class="forum-container">
        <h2 class="forum-title">Quarterly EU-CTR Forum</h2>
        <p class="forum-description">
            Krystelis hosts quarterly a closed forum where invited industry representatives can openly
            discuss issues, challenges and solutions regarding EU-CTR 536/2014 and CTIS.
        </p>

        <div class="forum-content">
            <!-- White Papers Column -->
            <div class="forum-column forum-column-whitepapers">
                <h3 class="column-title">White papers</h3>
                <p class="column-description">
                    Follow the link to access our latest white papers on
                    hot topics in the life sciences industry.
                </p>
                <a href="http://localhost/wordpress/whitepapers/" class="forum-btn">READ MORE</a>
            </div>

            <!-- Synthetic Data Column -->
            <div class="forum-column forum-column-highlight">
                <h3 class="column-title">Synthetic data and European General Data Protection Regulation: Ethics, quality and legality of data sharing</h3>
                <p class="column-description">
                    Synthetic data is increasingly being used across the financial services, clinical research,
                    manufacturing and transport industries. Download this paper to learn more about the
                    ethics, quality, and legality of data sharing.
                </p>
                <a href="#" class="download-link whitepaper-download"><span class="download-icon">»</span> DOWNLOAD WHITEPAPER</a>
            </div>

            <!-- CTIS Transparency Column -->
            <div class="forum-column forum-column-highlight">
                <h3 class="column-title">Riding the CTIS rollercoaster: What is the impact of the new transparency rules?</h3>
                <p class="column-description">
                    Following a public consultation, EMA has recently revised the EU CTR transparency
                    rules. This paper sheds light on these revisions.
                </p>
                <a href="#" class="download-link whitepaper-download"><span class="download-icon">»</span> DOWNLOAD WHITEPAPER</a>
            </div>
        </div>
    </div>
</section>

<!-- Conferences Section -->
<section class="conferences-section">
    <div class="conferences-container">
        <h3 class="conferences-subtitle">RESOURCES</h3>
        <h2 class="conferences-title">Conferences</h2>
        <p class="conferences-description">
            Learn more about our contributions to and presence at global forums and conferences.
        </p>
        <div class="conferences-button-container">
            <a href="http://localhost/wordpress/conferences/" class="conferences-btn">READ MORE</a>
        </div>
    </div>
</section>

<!-- Conference Cards Section -->
<section class="conference-cards-section">
    <div class="conference-cards-container">
        <div class="conference-cards-grid">
            <!-- Card 1 -->
            <div class="conference-card">
                <h3 class="conference-card-title">DIA India Annual Meeting 2024</h3>
                <p class="conference-speaker">Speaker: Industry Experts</p>
                <div class="conference-dates">
                    <span class="conference-date">September 24, 2024</span>
                    <span class="conference-date">September 25, 2024</span>
                </div>
                <a href="https://www.diaglobal.org/en/conference-listing/meetings/2024/09/confluence-of-care-pharmacovigilance-clinical-innovation-regulatory-sciences-and-medical-devices-elevating-patient-safety/register" class="conference-link">Link to the Conference</a>
            </div>

            <!-- Card 2 -->
            <div class="conference-card conference-card-large">
                <h3 class="conference-card-title">DIA Global Clinical Trial Disclosure and Data Transparency Conference 2024</h3>
                <p class="conference-speaker">Speaker: Industry Experts</p>
                <div class="conference-dates">
                    <span class="conference-date">September 23, 2024</span>
                    <span class="conference-date">September 24, 2024</span>
                </div>
                <a href="#" class="conference-link">Link to the Conference</a>
            </div>

            <!-- Card 3 -->
            <div class="conference-card">
                <h3 class="conference-card-title">20th Annual Meeting of ISMPP</h3>
                <p class="conference-speaker">Speaker: Industry Experts</p>
                <div class="conference-dates">
                    <span class="conference-date">April 29, 2024</span>
                    <span class="conference-date">May 1, 2024</span>
                </div>
                <a href="https://connect.ismpp.org/events/event-description?CalendarEventKey=30be9f62-c294-4793-bd77-01877129be32&Home=%2fevents%2fcalendar&hlmlt=ED" class="conference-link">Link to the Conference</a>
            </div>
        </div>
    </div>
</section>

<!-- Webinars Section -->
<section class="webinars-section">
    <div class="webinars-container">
        <div class="webinars-content">
            <!-- Left Column - Webinars Info -->
            <div class="webinars-info">
                <div class="webinars-brand">KRYSTELIS</div>
                <h2 class="webinars-title">Webinars</h2>
                <p class="webinars-description">
                    Learn more about past and upcoming webinars.
                </p>
                <a href="http://localhost/wordpress/our-webinars/" class="webinars-btn">READ MORE</a>
            </div>

            <!-- Right Column - Webinar Cards -->
            <div class="webinars-cards">
                <!-- Webinar Card 1 -->
                <div class="webinar-card">
                    <h3 class="webinar-card-title">Harnessing the Power of AI in Scientific Publications</h3>
                    <p class="webinar-speaker">Speaker: Renu Juneja, Elia Lima-Walton, Shalini Dwivedi</p>
                    <p class="webinar-date">July 11, 2024</p>
                    <p class="webinar-time">9:00 - 10:00 AM EDT / 2:00 – 3:00 PM BST</p>
                    <a href="http://localhost/wordpress/jobs/harnessing-the-power-of-ai-in-scientific-publications/" class="webinar-link">Click here for more details</a>
                </div>

                <!-- Webinar Card 2 -->
                <div class="webinar-card">
                    <h3 class="webinar-card-title">Best Practices for Writing & Disseminating Plain Language Summaries of Publications (PLSPs)</h3>
                    <p class="webinar-speaker">Speaker: Stephanie Dooley, Joanne Walker, Vicki Mooney, Vidhi Vashisht</p>
                    <p class="webinar-date">September 27, 2023</p>
                    <p class="webinar-time">10:00 - 11:00 AM EDT / 3:00 – 4:00 PM BST / 4:00 – 5:00 PM CEST</p>
                    <a href="http://localhost/wordpress/jobs/best-practices-for-writing-disseminating-plain-language-summaries-of-publications-plsps/" class="webinar-link">Click here for more details</a>
                </div>
            </div>
        </div>

        <!-- Decorative Triangle
        <div class="webinars-triangle"></div> -->
    </div>
</section>

<?php get_template_part('template-parts/cta-section'); ?>

<!-- Download Center Popup -->
<div id="download-popup" class="download-popup-overlay" style="display:none;">
  <div class="download-popup-modal">
    <div class="download-popup-header">
      <span class="download-popup-title">Krystelis Download Center</span>
      <span class="download-popup-close" id="download-popup-close">&times;</span>
    </div>
    <form class="download-popup-form">
      <input type="email" placeholder="Email Address" required>
      <input type="text" placeholder="First Name" required>
      <input type="text" placeholder="Last Name" required>
      <input type="text" placeholder="Designation" required>
      <input type="text" placeholder="Company" required>
      <button type="submit" class="download-popup-btn">GET DOWNLOAD LINK</button>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('.whitepaper-download').forEach(function(btn) {
    btn.addEventListener('click', function(e) {
      e.preventDefault();
      document.getElementById('download-popup').style.display = 'flex';
    });
  });
  document.getElementById('download-popup-close').onclick = function() {
    document.getElementById('download-popup').style.display = 'none';
  };
  window.onclick = function(event) {
    var popup = document.getElementById('download-popup');
    if (event.target === popup) {
      popup.style.display = 'none';
    }
  };

  const articleBoxes = document.querySelectorAll('.article-box');

  articleBoxes.forEach(box => {
    box.addEventListener('click', function() {
        articleBoxes.forEach(otherBox => {
            otherBox.classList.remove('active');
        });
        this.classList.add('active');
        setTimeout(() => {
            this.classList.remove('active');
        }, 2000);
    });

    box.addEventListener('mouseenter', function() {
        // No transform here
    });

    box.addEventListener('mouseleave', function() {
        // No transform here
    });
  });
});
</script>

<style>
/* Reset and base styles */
* {
    box-sizing: border-box;
}

/* Mobile Research Clarity Section */
.mobile-research-clarity {
    display: none;
    text-align: center;
    padding: 20px;
    background: #fff;
}

.mobile-research-clarity h3 {
    font-family: "Maven Pro", sans-serif;
    font-size: 1.75rem;
    font-weight: 500;
    color: #FF6A18;
    margin: 0;
    line-height: 1.5;
}

@media (max-width: 768px) {
    .mobile-research-clarity {
        display: block;
        margin-top: 20px;
    }
    
    .mobile-research-clarity h3 {
        font-size: 1.25rem;
        padding: 0 15px;
    }
}

@media (min-width: 769px) and (max-width: 992px) {
    .mobile-research-clarity {
        display: none;
    }
    .hero-subtitle {
        display: block;
    }
}

/* Insights Hero Section */
.insights-hero-wrapper {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    background: #fff;
    padding: 0;
    margin-top: 0;
    margin-bottom: 0;
}

.insights-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 500px;
    max-width: 100%;
    margin: 0;
    padding: 0;
    gap: 0;
}

.hero-text {
    padding: 40px 60px 60px 40px;
    background: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
}

.hero-subtitle {
    font-family: "Maven Pro", sans-serif;
    font-size: 1.75rem;
    font-weight: 500;
    color: #FF6A18;
    margin: 0 0 15px 0;
    line-height: 1.5;
    opacity: 0;
  transform: translateY(-100px);
  transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1), transform 1.2s cubic-bezier(0.22, 1, 0.36, 1);
}
.hero-subtitle.animated {
  opacity: 1;
  transform: translateY(0);
}


.hero-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 48px;
    font-weight: 700;
    color: #0072DA;
    margin: 30px 0 30px 0;
    line-height: 1.2;
    opacity: 0;
  transform: translateY(100px);
  transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1), transform 1.2s cubic-bezier(0.22, 1, 0.36, 1);
}
.hero-title.animated {
  opacity: 1;
  transform: translateY(0);
}

.hero-description {
    font-family: Georgia, serif;
    font-size: 22px;
    color: #525252;
    line-height: 1.6;
    margin: 0 0 20px 0;
    opacity: 0;
  transform: translateY(100px);
  transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1), transform 1.2s cubic-bezier(0.22, 1, 0.36, 1);
}
.hero-description.animated {
  opacity: 1;
  transform: translateY(0);
}


.hero-btn {
    display: inline-block !important;
    padding: 1.25rem 2.50rem !important;
    /* margin-left: 1rem !important; */
    text-decoration: none !important;
    border-radius: 15px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    border: none !important;
    box-shadow: 0px 0px 20px 0px #FFBA9D;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    cursor: pointer !important;
    font-size: 1.50rem !important;
    font-family: "Maven Pro", sans-serif !important;
    background: linear-gradient(90deg, #ff7f3f 0%, #ff6b2b 100%);
    box-shadow: 0px 0px 20px 0px #FFBA9D;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    color: white;
    opacity: 0;
  transform: translateY(180px);
  transition: opacity 1.5s cubic-bezier(0.22, 1, 0.36, 1), transform 1.5s cubic-bezier(0.22, 1, 0.36, 1);
}
.hero-btn.animated {
  opacity: 1;
  transform: translateY(0);
}

.hero-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 127, 63, 0.4);
    color: white;
    text-decoration: none;
}

.hero-image {
    position: relative;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding: 15px;
}

.hero-image img {
    width: 100%;
    height: 500px;
    boder-radius: 15px;
    display: block;
    opacity: 0;
  transform: scale(0.85);
  transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1), transform 1.2s cubic-bezier(0.22, 1, 0.36, 1);
}
.hero-image img.animated {
  opacity: 1;
  transform: scale(1);
}


.lightbulb-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.lightbulb-overlay svg {
    display: block;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }
}

/* Mobile responsive */
@media (max-width: 768px) {
    .insights-hero-content {
        grid-template-columns: 1fr;
        min-height: auto;
    }

    .hero-text {
        padding: 40px 30px;
        order: 2;
        text-align: center;
    }

    .hero-image {
        order: 1;
        min-height: 300px;
        padding: 15px;
    }

    .hero-image img {
        height: 300px;
        width: 100%;
        object-fit: cover;
        border-radius: 15px;
    }

    .hero-title {
        font-size: 36px;
        text-align: center;
        margin: 30px auto;
    }

    .hero-description {
        font-size: 18px;
        text-align: center;
    }

    .hero-subtitle {
        display: none;
    }

    .hero-btn {
        margin: 0 auto !important;
        display: block !important;
    }

    .lightbulb-overlay {
        padding: 20px;
    }

    .lightbulb-overlay svg {
        width: 80px;
        height: 80px;
    }
}

/* Articles Section */
articles-grid.articles-section {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    padding: 80px 20px;
    background: #f8f9fa;
}

.articles-container {
    max-width: 100%;
    margin: 0 20px;
    text-align: center;
}

.articles-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 48px;
    font-weight: 700;
    color: #0072DA;
    margin: 0 0 20px 0;
    line-height: 1.2;
    opacity: 0;
  transform: translateY(-100px);
  transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1), transform 1.2s cubic-bezier(0.22, 1, 0.36, 1);
}
.articles-title.animated {
  opacity: 1;
  transform: translateY(0);
}

.articles-subtitle {
    font-family: Georgia, serif;
    font-size: 22px;
    color: #525252;
    margin: 0 0 60px 0;
    line-height: 1.6;
    opacity: 0;
  transform: translateY(100px);
  transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1), transform 1.2s cubic-bezier(0.22, 1, 0.36, 1);
}
.articles-subtitle.animated {
  opacity: 1;
  transform: translateY(0);
}

/* .articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
} */

.articles-grid-five {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 30px;
    margin-top: 40px;
    opacity: 0;
  transform: translateX(120px);
  transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1), transform 1.2s cubic-bezier(0.22, 1, 0.36, 1);
}
.articles-grid-five.animated {
  opacity: 1;
  transform: translateX(0);
}

/* .articles-grid-five .article-box:nth-child(4),
.articles-grid-five .article-box:nth-child(5) {
    grid-row: 2;
}

.articles-grid-five .article-box:nth-child(4) {
    grid-column: 1 / 2;
}

.articles-grid-five .article-box:nth-child(5) {
    grid-column: 3 / 4;
} */

.article-box {
    position: relative;
    border-radius: 15px;
    overflow: visible;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
    display: flex;
    flex-direction: column;
    margin-bottom: 6rem;
}

.article-box:hover,
.article-box.active {
    /* Remove transform! */
    /* transform: translateY(-10px); */
    box-shadow: 0 15px 40px rgba(0, 114, 218, 0.3);
    /* Optionally: */
    /* transform: scale(1.03); */
}

.article-box:hover .article-overlay {
    background: rgba(0, 114, 218, 0.95);
    transform: translate(-50%, 50%);
}

.article-box.active .article-overlay {
    background: rgba(0, 114, 218, 0.95);
    transform: translate(-50%, 50%);
}

.article-image {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    flex: 1;
}

.article-image img {
    width: 100%;
    height: 100%;
    border-radius: 15px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.article-box:hover .article-image img,
.article-box.active .article-image img {
    transform: scale(1.05);
}

.article-overlay {
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translate(-50%, 50%);
    width: 90%;
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
    padding: 24px 20px 20px 20px;
    z-index: 2;
    text-align: left;
}

.article-overlay-orange {
    background: rgba(255, 255, 255, 0.95);
}

.article-date {
    font-family: "Georgiagit", sans-serif;
    font-size: 16px;
    color: #ff6a18;
    font-weight: 600;
    margin: 0 0 8px 0;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.article-box:hover .article-date {
    color: #FFFFFF;
}

.article-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 22px;
    font-weight: 600;
    color: #0072DA;
    margin: 0;
    line-height: 1.3;
    transition: all 0.3s ease;
}

.article-box:hover .article-title {
    color: white;
}

.article-box.active .article-title {
    color: white;
}

.article-box.active .article-date {
    color: #FFFFFF;
}

/* Second row of articles */
.articles-grid-second {
    margin-top: 30px;
    grid-template-columns: repeat(2, 1fr);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Read More Button */
.read-more-container {
    text-align: center;
    margin-top: 5rem;
}

.read-more-btn {
    display: inline-block !important;
    padding: 1.25rem 2.50rem !important;
    margin-left: 1rem !important;
    text-decoration: none !important;
    border-radius: 15px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    border: none !important;
    box-shadow: 0px 0px 20px 0px #FFBA9D;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    cursor: pointer !important;
    font-size: 1.50rem !important;
    font-family: "Maven Pro", sans-serif !important;
    background: linear-gradient(90deg, #ff9459 0%, #ff6b2b 100%);
    box-shadow: 0px 0px 20px 0px #FFBA9D;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    color: white;
    opacity: 0;
  transform: translateY(180px);
  transition: opacity 1.5s cubic-bezier(0.22, 1, 0.36, 1), transform 1.5s cubic-bezier(0.22, 1, 0.36, 1);
}
.read-more-btn.animated {
  opacity: 1;
  transform: translateY(0);
}

.read-more-btn:hover {
    background-color: #e55a2b;
    transform: translateY(-2px);
    color: white;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(255, 42, 5, 0.4); /* Brighter effect */
    text-decoration: none;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 20px 0pxrgb(251, 77, 3);
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}

/* Quarterly EU-CTR Forum Section */
.forum-section {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    padding: 80px 20px;
    background: #fff;
}

.forum-container {
    max-width: 100%;
    margin: 0 auto;
    text-align: center;
}

.forum-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 48px;
    font-weight: 700;
    color: #0072DA;
    margin: 0 0 20px 0;
    line-height: 1.2;
}

.forum-description {
    font-family: Georgia, serif;
    font-size: 22px;
    color: #525252;
    margin: 0 0 60px 0;
    line-height: 1.6;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
}

.forum-content {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 30px;
    margin-top: 40px;
}

.forum-column {
    background: transparent;
    border-radius: 15px;
    padding: 40px 30px;
    text-align: left;
    transition: all 0.3s ease;
    box-shadow: none;
    cursor: pointer;
    opacity: 0;
  transform: translateY(100px);
  transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1), transform 1.2s cubic-bezier(0.22, 1, 0.36, 1);
}
.forum-column.animated {
  opacity: 1;
  transform: translateY(0);
}

.forum-column:hover {
    background: transparent;
    transform: translateY(-5px);
    box-shadow: none;
}

.forum-column:hover .column-title {
    color: #0072DA;
}

.forum-column:hover .column-description {
    color: #525252;
}

.forum-column:hover .download-link {
    color: #ff7f3f;
}

.forum-column-highlight {
    background: white;
    border: 1px solid #e6f3ff;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.10);
    display: flex;
    flex-direction: column;
}

.column-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 24px;
    font-weight: 600;
    color: #0072DA;
    margin: 0 0 18px 0;
    line-height: 1.3;
    transition: all 0.3s ease;
}

.column-description {
    font-family: Georgia, serif;
    font-size:22px;
    color: #525252;
    line-height: 1.6;
    margin: 0 0 30px 0;
    flex-grow: 1;
}

.forum-btn {
    display: inline-block !important;
    padding: 1.25rem 2.50rem !important;
    /* margin-left: 1rem !important; */
    text-decoration: none !important;
    border-radius: 15px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    border: none !important;
    box-shadow: 0px 0px 20px 0pxrgb(255, 255, 255);
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    cursor: pointer !important;
    font-size: 1.50rem !important;
    font-family: "Maven Pro", sans-serif !important;
    background: linear-gradient(90deg, #ff7f3f 0%, #ff6b2b 100%);
    box-shadow: 0px 0px 20px 0px #FFBA9D;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    color: white;
}

.forum-btn:hover {
    background-color: #e55a2b;
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.download-link {
    display: inline-flex;
    align-items: center;
    color: #ff7f3f;
    text-decoration: none;
    font-family: "Maven Pro", sans-serif;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.download-link:hover {
    color: #ff6b2b;
    text-decoration: none;
}

.download-icon {
    margin-right: 8px;
    font-size: 16px;
    font-weight: bold;
}

/* Conferences Section */
.conferences-section {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    padding: 80px 20px;
    background: #f8f9fa;
}

.conferences-container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.conferences-subtitle {
    font-family: "Maven Pro", sans-serif;
    font-size: 1.75rem;
    font-weight: 500;
    color: #ff7f3f;
    margin: 0 0 15px 0;
    line-height: 1.5;
    text-transform: uppercase;
    letter-spacing: 8px;
    opacity: 0;
  transform: translateY(100px);
  transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1), transform 1.2s cubic-bezier(0.22, 1, 0.36, 1);
}
.conferences-subtitle.animated {
  opacity: 1;
  transform: translateY(0);
}

.conferences-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 48px;
    font-weight: 700;
    color: #0072DA;
    margin: 0 0 30px 0;
    line-height: 1.2;
    opacity: 0;
    transform: translateY(100px);
    transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1), transform 1.2s cubic-bezier(0.22, 1, 0.36, 1);
}
.conferences-title.animated {
    opacity: 1;
    transform: translateY(0);
}

.conferences-description {
    font-family: Georgia, serif;
    font-size: 22px;
    color: #525252;
    line-height: 1.6;
    margin: 0 0 40px 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 40px;
}

.conferences-button-container {
    text-align: center;
    margin-top: 30px;
}

.conferences-btn {
    display: inline-block !important;
    padding: 1.25rem 2.50rem !important;
    margin-left: 1rem !important;
    text-decoration: none !important;
    border-radius: 15px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    border: none !important;
    box-shadow: 0px 0px 20px 0px #FFBA9D;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    cursor: pointer !important;
    font-size: 1.50rem !important;
    font-family: "Maven Pro", sans-serif !important;
    background: linear-gradient(90deg, #ff7f3f 0%, #ff6b2b 100%);
    box-shadow: 0px 0px 20px 0px #FFBA9D;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    color: white;
    opacity: 0;
    transform: translateY(60px); /* Start further down for more visible movement */
    transition: opacity 2s cubic-bezier(0.22, 1, 0.36, 1), transform 2s cubic-bezier(0.22, 1, 0.36, 1); /* Slower and smoother */
}
.conferences-btn.animated {
    opacity: 1;
    transform: translateY(0);
}

.conferences-btn:hover {
    background-color: #e55a2b;
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

/* Conference Cards Section */
.conference-cards-section {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    padding: 80px 20px;
    background: #fff;
}

.conference-cards-container {
    margin: 0 auto;
}

.conference-cards-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 30px;
    align-items: start;
}

.conference-card {
    min-height: 100%;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    border: 1px solid #f0f0f0;
}

.conference-card:hover {
    background: #0072DA;
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 114, 218, 0.3);
}

.conference-card:hover .conference-card-title {
    color: white;
}

.conference-card:hover .conference-speaker {
    color: #e6f3ff;
}

.conference-card:hover .conference-date {
    color: #e6f3ff;
}

.conference-card:hover .conference-link {
    color: #ffba9d;
}

.conference-card-large {
    grid-row: span 1;
}

.conference-card-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 24px;
    font-weight: 600;
    color: #0072DA;
    margin: 0 0 15px 0;
    line-height: 1.3;
    transition: all 0.3s ease;
}

.conference-speaker {
    font-family: Georgia, serif;
    font-size: 14px;
    color: #666;
    margin: 0 0 20px 0;
    font-style: italic;
    transition: all 0.3s ease;
}

.conference-dates {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin: 0 0 20px 0;
}

.conference-date {
    font-family: "Maven Pro", sans-serif;
    font-size: 14px;
    color: #525252;
    font-weight: 500;
    transition: all 0.3s ease;
}

.conference-link {
    font-family: "Maven Pro", sans-serif;
    font-size: 14px;
    font-weight: 600;
    color: #ff7f3f;
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.conference-link:hover {
    text-decoration: none;
}

/* Webinars Section */
.webinars-section {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    padding: 80px 20px;
    background: #f8f9fa;
    overflow: hidden;
}

.webinars-container {
    margin: 0 auto;
    position: relative;
    padding: 0 20px;
    max-width: 1400px;
}

.webinars-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 5px;
    align-items: start;
    position: relative;
    width: 100%;
}

.webinars-info {
    padding-right: 40px;
    padding-top: 20px;
    margin-left: -30px;
    opacity: 0;
    transform: translateY(60px);
    transition: opacity 2s cubic-bezier(0.22, 1, 0.36, 1), transform 2s cubic-bezier(0.22, 1, 0.36, 1);
}
.webinars-info.animated {
    opacity: 1;
    transform: translateY(0);
}

.webinars-brand {
    font-family: "Georgia", sans-serif;
    font-size: 20px;
    font-weight: 500;
    color: #ff7f3f;
    margin: 0 0 15px 0;
    text-transform: uppercase;
    letter-spacing: 8px;
}

.webinars-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 52px;
    font-weight: 700;
    color: #0072DA;
    margin: 0 0 25px 0;
    line-height: 1.1;
}

.webinars-description {
    font-family: Georgia, serif;
    font-size: 24px;
    color: #525252;
    line-height: 1.6;
    margin: 0 0 35px 0;
}

.webinars-btn {
    display: inline-block !important;
    padding: 1.25rem 2.50rem !important;
    /* margin-left: 1rem !important; */
    text-decoration: none !important;
    border-radius: 15px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    border: none !important;
    box-shadow: 0px 0px 20px 0px #FFBA9D;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    cursor: pointer !important;
    font-size: 1.50rem !important;
    font-family: "Maven Pro", sans-serif !important;
    background: linear-gradient(90deg, #ff7f3f 0%, #ff6b2b 100%);
    box-shadow: 0px 0px 20px 0px #FFBA9D;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    color: white;
}

.webinars-btn:hover {
    background-color: #e55a2b;
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.webinars-cards {
    position: relative;
    height: 500px;
    width: 100%;
    padding-right: 20px;
    display: flex;
    gap: 30px;
}

.webinar-card {
    position: relative;
    background: white;
    border-radius: 15px;
    padding: 35px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid #e8e8e8;
    width: calc(50% - 15px);
    min-height: 74%;
}

.webinar-card:first-child {
    z-index: 2;
}

.webinar-card:last-child {
    z-index: 1;
}

.webinar-card:hover {
    background: #0072DA;
    transform: translateY(-8px);
    box-shadow: 0 20px 45px rgba(0, 114, 218, 0.25);
}

.webinar-card:hover .webinar-card-title {
    color: white;
}

.webinar-card:hover .webinar-speaker {
    color: #e6f3ff;
}

.webinar-card:hover .webinar-date {
    color: #e6f3ff;
}

.webinar-card:hover .webinar-time {
    color: #e6f3ff;
}

.webinar-card:hover .webinar-link {
    color: #ffba9d;
}

.webinar-card-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 22px;
    font-weight: 600;
    color: #0072DA;
    margin: 0 0 18px 0;
    line-height: 1.3;
    transition: all 0.3s ease;
}

.webinar-speaker {
    font-family: Georgia, serif;
    font-size: 15px;
    color: #666;
    margin: 0 0 15px 0;
    font-style: italic;
    transition: all 0.3s ease;
    line-height: 1.4;
}

.webinar-date {
    font-family: "Maven Pro", sans-serif;
    font-size: 17px;
    color: #333;
    font-weight: 600;
    margin: 0 0 10px 0;
    transition: all 0.3s ease;
}

.webinar-time {
    font-family: "Maven Pro", sans-serif;
    font-size: 15px;
    color: #666;
    margin: 0 0 20px 0;
    transition: all 0.3s ease;
    line-height: 1.4;
}

.webinar-link {
    font-family: "Maven Pro", sans-serif;
    font-size: 15px;
    font-weight: 600;
    color: #ff7f3f;
    text-decoration: none;
    transition: all 0.3s ease;
}

.webinar-link:hover {
    text-decoration: none;
    color: #ff6b2b;
}

/* Decorative Triangle
.webinars-triangle {
    position: absolute;
    top: -166px;
    right: -100px;
    width: 0;
    height: 0;
    border-left: 200px solid #b8d4ff;
    border-bottom: 300px solid transparent;
    opacity: 0.6;
    z-index: 0;
} */

/* Mobile responsive for articles */
@media (max-width: 768px) {
    .articles-section {
        padding: 60px 20px;
    }

    .articles-title {
        font-size: 36px;
    }

    .articles-subtitle {
        font-size: 18px;
        margin-bottom: 40px;
    }

    .articles-grid,
    .articles-grid-five {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
        gap: 60px;
        max-width: 100%;
    }

    .articles-grid-five .article-box:nth-child(4),
    .articles-grid-five .article-box:nth-child(5) {
        grid-row: auto;
        grid-column: auto;
    }

    .article-image {
        height: 250px;
    }

    .article-overlay {
        padding: 15px;
    }

    .article-title {
        font-size: 16px;
    }

    .article-date {
        font-size: 12px;
    }

    .read-more-btn {
        padding: 12px 30px;
        font-size: 14px;
    }

    /* Forum section mobile styles */
    .forum-section {
        padding: 60px 20px;
    }

    .forum-title {
        font-size: 36px;
    }

    .forum-description {
        font-size: 16px;
        margin-bottom: 40px;
    }

    .forum-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .forum-column {
        padding: 30px 20px;
        text-align: center;
    }

    .column-title {
        font-size: 20px;
        text-align: center;
    }

    .column-description {
        font-size: 15px;
        text-align: center;
    }

    .forum-btn {
        margin: 0 auto !important;
        display: block !important;
    }

    .download-link {
        justify-content: center;
        margin: 0 auto;
    }

    /* Conferences section mobile styles */
    .conferences-section {
        padding: 60px 20px;
    }

    .conferences-title {
        font-size: 36px;
    }

    .conferences-description {
        font-size: 18px;
        margin-bottom: 30px;
    }

    .conferences-btn {
        padding: 12px 30px;
        font-size: 14px;
    }

    /* Conference cards mobile styles */
    .conference-cards-section {
        padding: 60px 20px;
    }

    .conference-cards-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .conference-card {
        padding: 25px 20px;
    }

    .conference-card-title {
        font-size: 18px;
    }

    .conference-speaker {
        font-size: 13px;
    }

    .conference-date {
        font-size: 13px;
    }

    .conference-link {
        font-size: 13px;
    }

    /* Webinars section mobile styles */
    .webinars-section {
        padding: 60px 20px;
    }

    .webinars-content {
        grid-template-columns: 1fr;
        gap: 50px;
    }

    .webinars-info {
        padding-left: 20px;
        padding-right: 20px;
        margin-left: 0;
        padding-top: 0;
        text-align: center;
    }

    .webinars-brand {
        font-size: 14px;
        letter-spacing: 2px;
    }

    .webinars-title {
        font-size: 32px;
    }

    .webinars-description {
        font-size: 16px;
        margin-bottom: 30px;
    }

    .webinars-btn {
        padding: 14px 30px;
        font-size: 15px;
    }

    .webinars-cards {
        height: auto !important;
        display: flex;
        flex-direction: column;
        gap: 25px;
        padding-right: 0;
    }

    .webinar-card {
        width: 100% !important;
        max-width: 100% !important;
        padding: 25px 20px;
    }

    .webinar-card-title {
        font-size: 18px;
    }

    .webinar-speaker {
        font-size: 14px;
    }

    .webinar-date {
        font-size: 16px;
    }

    .webinar-time {
        font-size: 14px;
    }

    .webinar-link {
        font-size: 14px;
    }

    .webinars-triangle {
        display: none;
    }
}

@media (max-width: 1439px) {
    .webinars-info {
        padding-left: 40px;
        padding-right: 40px;
    }
}
@media (max-width: 1200px) {
    .webinars-info {
        padding-left: 20px;
        padding-right: 20px;
    }
}
@media (max-width: 992px) {
    .webinars-info {
        padding-left: 20px;
        padding-right: 20px;
        margin-left: 0;
        padding-top: 0;
        text-align: center;
    }
}
@media (max-width: 768px) {
    .webinars-info {
        padding-left: 10px;
        padding-right: 10px;
    }
}

/* CTA Section Styling */
.insights-cta-section {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    padding: 4rem 1rem;
    background: #f8f9fa;
    overflow: hidden;
}

.insights-cta-container {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.insights-cta-background {
    max-width: 1100px;
    width: 100%;
    padding: 4rem 3rem;
    border-radius: 20px;
    background: linear-gradient(135deg, #4A90E2 0%, #0072DA 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    box-shadow: 0 10px 40px rgba(0, 114, 218, 0.2);
}

.insights-cta-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 900px;
    margin: 0 auto;
}

.insights-cta-subtitle {
    font-size: 1rem;
    font-weight: 400;
    letter-spacing: 2px;
    margin-bottom: 1rem;
    opacity: 0.9;
    font-family: "Maven Pro", sans-serif;
    text-transform: uppercase;
}

.insights-cta-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 2rem;
    line-height: 1.2;
    font-family: "Maven Pro", sans-serif;
    color: white;
}

.insights-cta-button {
    display: inline-block;
    background: white;
    color: #0072DA;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: "Maven Pro", sans-serif;
    letter-spacing: 1px;
    text-transform: uppercase;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.insights-cta-button:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    color: #0072DA;
    text-decoration: none;
}

/* Decorative Shapes */
.cta-shape-left {
    position: absolute;
    left: -50px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    opacity: 0.8;
}

.cta-shape-left img {
    width: 150px;
    height: auto;
    display: block;
}

.cta-shape-right {
    position: absolute;
    right: -50px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    opacity: 0.7;
}

.cta-shape-right img {
    width: 200px;
    height: auto;
    display: block;
}

/* CTA Section Responsive */
@media (max-width: 768px) {
    .insights-cta-section {
        padding: 2rem 0.5rem;
    }

    .insights-cta-background {
        padding: 3rem 1.5rem;
        border-radius: 15px;
        max-width: 90%;
    }

    .insights-cta-title {
        font-size: 2.5rem;
    }

    .insights-cta-subtitle {
        font-size: 0.9rem;
    }

    .insights-cta-button {
        padding: 0.875rem 2rem;
        font-size: 0.9rem;
    }

    .cta-shape-left {
        left: -30px;
    }

    .cta-shape-left img {
        width: 150px;
    }

    .cta-shape-right {
        right: -30px;
    }

    .cta-shape-right img {
        width: 180px;
    }
}

@media (max-width: 480px) {
    .insights-cta-background {
        padding: 2.5rem 1rem;
        border-radius: 12px;
    }

    .insights-cta-title {
        font-size: 2rem;
    }

    .insights-cta-subtitle {
        font-size: 0.8rem;
        letter-spacing: 1px;
    }

    .insights-cta-button {
        padding: 0.75rem 1.5rem;
        font-size: 0.85rem;
    }

    .cta-shape-left {
        display: none;
    }

    .cta-shape-right {
        display: none;
    }
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .articles-grid-five {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }

    .article-box {
        height: 380px;
    }

    .forum-content {
        grid-template-columns: 1fr 1fr;
        gap: 25px;
    }

    .forum-column:first-child {
        grid-column: 1 / -1;
    }
}

@media (max-width: 992px) {
    .webinars-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    .webinars-info {
        text-align: center;
        padding-right: 0;
        margin-left: 0;
        padding-top: 0;
    }
}

@media (max-width: 768px) {
    .articles-grid-five {
        grid-template-columns: 1fr;
        gap: 60px;
    }

    .article-box {
        height: 250px;
        margin-bottom: 30px;
    }

    .article-overlay {
        padding: 20px;
    }

    .article-title {
        font-size: 20px;
    }

    .article-date {
        font-size: 14px;
    }

    .forum-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .column-title {
        font-size: 26px;
    }

    .column-description {
        font-size: 16px;
    }

    .conference-cards-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .conference-card {
        padding: 25px 20px;
    }

    .conference-card-title {
        font-size: 20px;
    }

    .conference-speaker {
        font-size: 14px;
    }

    .conference-date {
        font-size: 14px;
    }

    .conference-link {
        font-size: 14px;
    }

    .webinars-title {
        font-size: 32px;
    }

    .webinars-description {
        font-size: 16px;
    }

    .webinar-card-title {
        font-size: 18px;
    }

    .webinar-speaker {
        font-size: 14px;
    }

    .webinar-date {
        font-size: 16px;
    }

    .webinar-time {
        font-size: 14px;
    }

    .webinar-link {
        font-size: 14px;
    }
}

@media (max-width: 576px) {
    .article-box {
        height: 250px;
        margin-bottom: 30px;
    }

    .article-overlay {
        padding: 15px;
    }

    .article-title {
        font-size: 18px;
    }

    .article-date {
        font-size: 13px;
    }

    .column-title {
        font-size: 24px;
    }

    .column-description {
        font-size: 15px;
    }

    .forum-btn {
        padding: 1rem 2rem !important;
        font-size: 1.25rem !important;
    }

    .conference-card {
        padding: 20px 15px;
    }

    .conference-card-title {
        font-size: 18px;
    }

    .conference-speaker {
        font-size: 13px;
    }

    .conference-date {
        font-size: 13px;
    }

    .conference-link {
        font-size: 13px;
    }

    .webinar-card {
        padding: 25px 20px;
    }

    .webinar-card-title {
        font-size: 18px;
    }

    .webinar-speaker {
        font-size: 14px;
    }

    .webinar-date {
        font-size: 15px;
    }

    .webinar-time {
        font-size: 14px;
    }

    .webinar-link {
        font-size: 14px;
    }
}

@media (max-width: 400px) {
    .article-box {
        height: 320px;
    }

    .article-overlay {
        padding: 12px;
    }

    .article-title {
        font-size: 16px;
    }

    .article-date {
        font-size: 12px;
    }

    .column-title {
        font-size: 22px;
    }

    .column-description {
        font-size: 14px;
    }

    .forum-btn {
        padding: 0.875rem 1.75rem !important;
        font-size: 1.125rem !important;
    }
}

.gallery-container {
    position: relative;
    width: 100%;
    height: 300px;
    overflow: hidden;
    padding: 20px 0;
}

.gallery-track {
    display: flex;
    gap: 10px;
    height: 100%;
    padding: 0 60px;
    transition: transform 1.1s ease;
    align-items: center;
}

.gallery-nav {
    position: fixed;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    color: #0072DA;
    border: none;
    width: 50px;
    height: 50px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 100;
    border-radius: 50%;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.gallery-nav:hover {
    background: #0072DA;
    color: white;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 114, 218, 0.3);
}

.gallery-nav.gallery-prev {
    left: 20px;
}

.gallery-nav.gallery-next {
    right: 20px;
}

@media (max-width: 768px) {
    .gallery-container {
        height: 250px;
        margin-top: 4rem;
    }

    .gallery-track {
        padding: 0 50px;
        gap: 10px;
    }

    .gallery-item {
        height: 200px;
    }

    .gallery-nav {
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.95);
    }

    .gallery-nav.gallery-prev {
        left: 10px;
    }

    .gallery-nav.gallery-next {
        right: 10px;
    }
}

.article-categories {
    margin-top: 10px;
}
/* .article-category {
    display: inline-block;
    background: #f0f0f0;
    color: #0072DA;
    font-size: 13px;
    padding: 2px 10px;
    border-radius: 8px;
    margin-right: 5px;
} */

.no-image-text {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f0f0;
    color: #888;
    font-size: 1.2rem;
    font-family: "Maven Pro", sans-serif;
    min-height: 200px;
}

/* Download Popup Styles */
.download-popup-overlay {
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(22, 134, 224, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
}

.download-popup-modal {
    background: #fff;
    border-radius: 16px;
    max-width: 500px;
    width: 95%;
    box-shadow: 0 8px 32px rgba(22, 134, 224, 0.18);
    padding: 0 0 2.5rem 0;
    position: relative;
    animation: popupIn 0.2s;
}

@keyframes popupIn {
    from {
        transform: scale(0.95);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.download-popup-header {
    background: #4bb2ff;
    border-radius: 16px 16px 0 0;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.download-popup-title {
    color: #fff;
    font-size: 1.6rem;
    font-weight: 600;
    font-family: 'Maven Pro', Arial, sans-serif;
}

.download-popup-close {
    color: #fff;
    font-size: 2rem;
    font-weight: 700;
    cursor: pointer;
    margin-left: 1rem;
    transition: color 0.2s;
}

.download-popup-close:hover {
    color: #ff6c2d;
}

.download-popup-form {
    display: flex;
    flex-direction: column;
    gap: 1.1rem;
    margin: 2.5rem 0 0 0;
    align-items: center;
}

.download-popup-form input {
    width: 90%;
    max-width: 420px;
    padding: 1.1rem 1.2rem;
    border: none;
    border-radius: 20px;
    background: #ededed;
    font-size: 1.15rem;
    color: #888;
    font-family: 'Maven Pro', Arial, sans-serif;
    margin: 0 auto;
    outline: none;
    text-align: left;
}

.download-popup-form input::placeholder {
    color: #bdbdbd;
    opacity: 1;
}

.download-popup-btn {
    width: 90%;
    max-width: 300px;
    margin: 1.2rem auto 0 auto;
    padding: 1.1rem 0;
    border: none;
    border-radius: 16px;
    background: linear-gradient(90deg, #ff914d 0%, #ff6c2d 100%);
    color: #fff;
    font-size: 1.15rem;
    font-weight: 700;
    font-family: 'Maven Pro', Arial, sans-serif;
    cursor: pointer;
    transition: background 0.2s, box-shadow 0.2s;
    box-shadow: 0 4px 16px rgba(255, 108, 45, 0.13);
    text-transform: uppercase;
}

.download-popup-btn:hover {
    background: linear-gradient(90deg, #ff6c2d 0%, #ff914d 100%);
}

/* Mobile responsive for popup */
@media (max-width: 768px) {
    .download-popup-modal {
        width: 95%;
        max-width: 400px;
    }

    .download-popup-title {
        font-size: 1.4rem;
    }

    .download-popup-form input {
        font-size: 1rem;
        padding: 1rem 1.1rem;
    }

    .download-popup-btn {
        font-size: 1rem;
        padding: 1rem 0;
    }
}

@media (max-width: 480px) {
    .download-popup-modal {
        width: 98%;
        max-width: 350px;
    }

    .download-popup-header {
        padding: 0.8rem 1.2rem;
    }

    .download-popup-title {
        font-size: 1.2rem;
    }

    .download-popup-form {
        margin: 2rem 0 0 0;
        gap: 1rem;
    }

    .download-popup-form input {
        width: 95%;
        font-size: 0.9rem;
        padding: 0.9rem 1rem;
    }

    .download-popup-btn {
        width: 95%;
        font-size: 0.9rem;
        padding: 0.9rem 0;
    }
}

.forum-content .forum-column:first-child .column-title {
    font-size: 48px;
    margin-top: 2rem;
}

.forum-content .forum-column:first-child .column-description {
    font-size: 1.4rem;
}

.forum-column-whitepapers .column-title {
    font-size: 2.2rem;
}
.forum-column-whitepapers .column-description {
    font-size: 1.4rem;
    color:#525252;
}

</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const articleBoxes = document.querySelectorAll('.article-box');

    articleBoxes.forEach(box => {
        box.addEventListener('click', function() {
            articleBoxes.forEach(otherBox => {
                otherBox.classList.remove('active');
            });
            this.classList.add('active');
            setTimeout(() => {
                this.classList.remove('active');
            }, 2000);
        });

        box.addEventListener('mouseenter', function() {
            // No transform here
        });

        box.addEventListener('mouseleave', function() {
            // No transform here
        });
    });
});

function animateHeroSubtitleOnScroll() {
  var subtitles = document.querySelectorAll('.hero-subtitle');
  var windowHeight = window.innerHeight;
  subtitles.forEach(function(subtitle) {
    var position = subtitle.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      subtitle.classList.add('animated');
    }
  });
}
window.addEventListener('scroll', animateHeroSubtitleOnScroll);
document.addEventListener('DOMContentLoaded', animateHeroSubtitleOnScroll);

function animateHeroTitleOnScroll() {
  var titles = document.querySelectorAll('.hero-title');
  var windowHeight = window.innerHeight;
  titles.forEach(function(title) {
    var position = title.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      title.classList.add('animated');
    }
  });
}
window.addEventListener('scroll', animateHeroTitleOnScroll);
document.addEventListener('DOMContentLoaded', animateHeroTitleOnScroll);

function animateHeroDescriptionOnScroll() {
  var descriptions = document.querySelectorAll('.hero-description');
  var windowHeight = window.innerHeight;
  descriptions.forEach(function(desc) {
    var position = desc.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      desc.classList.add('animated');
    }
  });
}
window.addEventListener('scroll', animateHeroDescriptionOnScroll);
document.addEventListener('DOMContentLoaded', animateHeroDescriptionOnScroll);

function animateHeroImageOnScroll() {
  var images = document.querySelectorAll('.hero-image img');
  var windowHeight = window.innerHeight;
  images.forEach(function(img) {
    var position = img.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      img.classList.add('animated');
    }
  });
}
window.addEventListener('scroll', animateHeroImageOnScroll);
document.addEventListener('DOMContentLoaded', animateHeroImageOnScroll);

function animateHeroBtnOnScroll() {
  var btns = document.querySelectorAll('.hero-btn');
  var windowHeight = window.innerHeight;
  btns.forEach(function(btn) {
    var position = btn.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      btn.classList.add('animated');
    }
  });
}
window.addEventListener('scroll', animateHeroBtnOnScroll);
document.addEventListener('DOMContentLoaded', animateHeroBtnOnScroll);

function animateArticlesTitleOnScroll() {
  var titles = document.querySelectorAll('.articles-title');
  var windowHeight = window.innerHeight;
  titles.forEach(function(title) {
    var position = title.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      title.classList.add('animated');
    }
  });
}
window.addEventListener('scroll', animateArticlesTitleOnScroll);
document.addEventListener('DOMContentLoaded', animateArticlesTitleOnScroll);

function animateArticlesSubtitleOnScroll() {
  var subtitles = document.querySelectorAll('.articles-subtitle');
  var windowHeight = window.innerHeight;
  subtitles.forEach(function(subtitle) {
    var position = subtitle.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      subtitle.classList.add('animated');
    }
  });
}
window.addEventListener('scroll', animateArticlesSubtitleOnScroll);
document.addEventListener('DOMContentLoaded', animateArticlesSubtitleOnScroll);

function animateArticlesGridOnScroll() {
  var grids = document.querySelectorAll('.articles-grid-five');
  var windowHeight = window.innerHeight;
  grids.forEach(function(grid) {
    var position = grid.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      grid.classList.add('animated');
    }
  });
}
window.addEventListener('scroll', animateArticlesGridOnScroll);
document.addEventListener('DOMContentLoaded', animateArticlesGridOnScroll);

function animateReadMoreBtnOnScroll() {
  var btns = document.querySelectorAll('.read-more-btn');
  var windowHeight = window.innerHeight;
  btns.forEach(function(btn) {
    var position = btn.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      btn.classList.add('animated');
    }
  });
}
window.addEventListener('scroll', animateReadMoreBtnOnScroll);
document.addEventListener('DOMContentLoaded', animateReadMoreBtnOnScroll);

function animateForumColumnOnScroll() {
  var columns = document.querySelectorAll('.forum-column');
  var windowHeight = window.innerHeight;
  columns.forEach(function(column) {
    var position = column.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      column.classList.add('animated');
    }
  });
}
window.addEventListener('scroll', animateForumColumnOnScroll);
document.addEventListener('DOMContentLoaded', animateForumColumnOnScroll);

function animateConferencesSubtitleOnScroll() {
  var subtitles = document.querySelectorAll('.conferences-subtitle');
  var windowHeight = window.innerHeight;
  subtitles.forEach(function(subtitle) {
    var position = subtitle.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      subtitle.classList.add('animated');
    }
  });
}
window.addEventListener('scroll', animateConferencesSubtitleOnScroll);
document.addEventListener('DOMContentLoaded', animateConferencesSubtitleOnScroll);

function animateConferencesTitleOnScroll() {
  var titles = document.querySelectorAll('.conferences-title');
  var windowHeight = window.innerHeight;
  titles.forEach(function(title) {
    var position = title.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      title.classList.add('animated');
    }
  });
}
window.addEventListener('scroll', animateConferencesTitleOnScroll);
document.addEventListener('DOMContentLoaded', animateConferencesTitleOnScroll);


function animateConferencesBtnOnScroll() {
  var btns = document.querySelectorAll('.conferences-btn');
  var windowHeight = window.innerHeight;
  btns.forEach(function(btn) {
    var position = btn.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      btn.classList.add('animated');
    }
  });
}
window.addEventListener('scroll', animateConferencesBtnOnScroll);
document.addEventListener('DOMContentLoaded', animateConferencesBtnOnScroll);

function animateWebinarsInfoOnScroll() {
  var infos = document.querySelectorAll('.webinars-info');
  var windowHeight = window.innerHeight;
  infos.forEach(function(info) {
    var position = info.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      info.classList.add('animated');
    }
  });
}
window.addEventListener('scroll', animateWebinarsInfoOnScroll);
document.addEventListener('DOMContentLoaded', animateWebinarsInfoOnScroll);
</script>

<?php get_footer(); ?>
